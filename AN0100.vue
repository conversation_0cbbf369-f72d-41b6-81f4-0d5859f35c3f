<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            label-position="top"
            size="mini"
        >
            <el-form-item v-for="item in formList" :key="item.key" :prop="item.key" :label="item.label" :rules="item.rules">
                <el-checkbox-group v-model="form[item.key]" @change="handleChange(item.key)">
                    <el-checkbox v-for="label in item.options" :key="label" :label="label" :value="label" class="!m-2" border></el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <template  v-if="subFormList.length > 0">
              <label>1.1、过去1周内，上述皮肤的症状持续多少天</label>
              <el-form-item v-for="subItem in subFormList" :key="subItem.key" :prop="subItem.key" :label="subItem.label" :rules="[{required: true, message: '请选择', trigger: 'blur'}]">
                  <el-radio-group v-model="form[subItem.key]" v-if="subItem.options.length > 0">
                      <el-radio v-for="label in subItem.options" :key="label" :label="label" :value="label" class="!m-2" border></el-radio>
                  </el-radio-group>
              </el-form-item>
            </template>
        </el-form>
    </div>
</template>

<script>
const formList = [{
  label: '1、过去1周内，您的皮肤是否因为特应性皮炎/湿疹而导致以下症状',
  key: 'p1',
  type: 'checkbox',
  options: ['皮肤瘙痒', '睡眠受影响', '出血', '渗液', '开裂', '脱屑', '皮肤干燥粗糙', '不存在以上症状'],
  rules: [{
    required: true,
    message: '请选择',
    trigger: 'blur'
  }]
} ]
const subOptions = ['0天', '1-2天', '3-4天', '5-6天', '每天']
const sortList = ['皮肤瘙痒', '睡眠受影响', '出血', '渗液', '开裂', '脱屑', '皮肤干燥粗糙']
const optionMap = {
  '皮肤瘙痒': 'p1_1',
  '睡眠受影响': 'p1_2',
  '出血': 'p1_3',
  '渗液': 'p1_4',
  '开裂': 'p1_5',
  '脱屑': 'p1_6',
  '皮肤干燥粗糙': 'p1_7',
}
export default {
    name: "AN0100",
    props: {
        formData: String,
        context: {
          type: Object,
          default: () => ({
              person: {}
          })
        }
    },
    mounted() {
       if (this.formData) {
            this.form = JSON.parse(this.formData);
        }
    },
    computed: {
      subFormList() {
        if(this.form.p1.length ===1 && this.form.p1[0] === '不存在以上症状') {
          return [];
        }
        return this.form.p1.sort((a, b) => {
          return sortList.indexOf(a) - sortList.indexOf(b);
        }).map((item, index) => {
          return {
            key: optionMap[item],
            label: `（${index+1}）${item}`,
            options: subOptions,
          }
        })
      }
     },
    data() {
        return {
            formList,
            form: {
              p1: [],
              p1_1: null,
              p1_2: null,
              p1_3: null,
              p1_4: null,
              p1_5: null,
              p1_6: null,
              p1_7: null,
            },
        };
    },
    methods: {
      getData() {
          return this.form;
      },
      validateData() {
          return new Promise((resolve) => {
            this.$refs["form"].validate((valid) => {
                resolve(valid);
            });
          });
      },
      handleChange(key) {
        if(this.form[key].includes('不存在以上症状')) {
            this.form[key] = ['不存在以上症状']
        }
      },
      getSubKey(key) {
        return optionMap[key];
      }
    },
};

export const formConfig = {
  fieldList: [{
    label: '1、过去1周内，您的皮肤是否因为特应性皮炎/湿疹而导致以下症状',
    key: 'p1',
  } ]
}
</script>
<style scoped>
</style>
