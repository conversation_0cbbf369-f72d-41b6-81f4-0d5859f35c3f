<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            label-position="top"
            size="mini"
        >
          <h4 class="text-xl leading-8 text-center mb-4">哮喘及过敏性疾病筛查和管理研究知情同意书</h4>
            <div class="text-md leading-8 flex flex-col gap-2">
              <p>您被邀请参加这项研究是因为您符合本研究14岁及以上青年人群或60岁及以上老年人群的入组条件。本研究的研究医生或者研究人员会为您充分解释知情同意书的内容，请仔细阅读本知情同意书后慎重做出是否参加研究的决定。若您正在参加别的研究，请告知您的研究医生或者研究人员。</p>
              <h4 class="font-bold">一、研究背景及目的</h4>
              <p>本研究的研究背景：</p>
              <p>过敏性疾病是特应性个体暴露于过敏原后主要由IgE介导的慢性非感染性炎症反应。随着城市化进程的不断深入，加之环境污染、气候变化以及国民饮食结构等变化，过敏性疾病患病率在全球日趋增高，已成为新世纪全球性公共健康问题，目前已被列为全球第六大慢性疾病。在我国，过敏性疾病的患病率也呈快速上升趋势，且相当一部分患者未得到诊断和治疗。我国过敏性疾病管理正面临着诸多挑战：公众和患者对于过敏性疾病的认知普遍不足；患者依从性差，缺乏系统化随访监测；患者个体差异大，缺乏个性化诊治指导等。随着数字健康技术的发展，利用移动互联网技术构建数字化慢病管理工具为过敏性疾病高危人群的管理提供了新的思路。国外已有多个数字化管理系统走入临床并在慢病管理中显示出有益作用，而我国虽数字化起步较晚，但我国数字化基础好，创新能力强，有望开创更加完善的过敏性疾病数字化管理体系。本研究拟开展前瞻性队列研究，多中心、前瞻性、随机对照研究，以及多中心、前瞻性、整群随机对照研究。本研究中，我们首先使用团队自主研发的过敏性疾病筛查问卷，对多个场景如中学和高校在校学生以及社区人群等进行筛查，将高危人群纳入数字化管理体系，形成哮喘及过敏性疾病高危人群队列。运用数字化软件进行基线数据记录、常规监测和定期随访，为高危人群建立数字化健康档案。同时根据过敏原筛查、常规肺功能及支气管舒张试验结果等结果，将队列人群标签化处理，根据不同的标签给予个性化诊治建议和健康指导。在此基础上，我们将探讨过敏性疾病的发生、发展及其关键影响因素，针对不同标签人群给予不同的干预措施并进行效果评价以探索具有较高成本效益的过敏性疾病干预措施以及健康照护新策略。本研究将充分应用数字健康技术，促进医疗卫生与健康资源更深层次的整合和利用，提升健康治理能力，为“健康中国”战略的实施提供有力支持。</p>
              <p>本研究的研究目的：</p>
              <p>本研究旨在系统性地探索哮喘和过敏性疾病的高危人群筛查与管理模式，并通过多维度、多场景的实践应用，深入分析疾病的发生发展机制及其影响因素。通过构建覆盖不同地理区域、经济发展水平以及应用场景的筛查与管理模式，本研究致力于实现以下核心目标：一是建立高危人群筛查与患者管理的一体化模式，以提高疾病的早期发现率和管理效率；二是通过疾病控制优先（Disease Control Priority, DCP）理念，识别关键影响因素并设计有效的干预措施，同时对其实施效果进行科学评价；三是运用数字健康技术和人工智能等前沿工具，建立高危人群的筛查、评估与管理平台，并构建过敏性疾病综合评估与管理大模型，以实现精准的个性化疾病管理方案。最终，本研究期望为哮喘和过敏性疾病的防控提供科学依据和技术支持，推动相关公共卫生策略的优化与实施，提升患者的疾病管理质量和生活质量。</p>
              <h4 class="font-bold">二、参与人数</h4>
              <p>大约9728人将在北京、吉林、河北、浙江、湖北等省市的中学、高校及社区参与本项研究。</p>
              <h4 class="font-bold">三、研究内容</h4>
              <p>（一）研究设计：前瞻性队列研究；多中心、前瞻性、随机对照研究；多中心、前瞻性、整群随机对照研究</p>
              <p>（二）入选标准</p>
              <p>1.中学及高校研究对象：（1）年龄14岁及以上；（2）具有较好的配合度，能够理解并自行完成问卷。</p>
              <p>2.社区研究对象：（1）年龄60岁及以上；（2）调查前居住在调查抽样所选择的地区满12个月以上，并预计每年居住时间不少于6个月；（3）中国籍公民；（4）具有较好的配合度，能够理解并自行完成问卷</p>
              <p>（三）排除标准</p>
              <p>1.中学及高校研究对象：（1）学生本人或其监护人不同意参与研究，未能签署知情同意书者；（2）合并有严重慢性疾病（如肿瘤、晚期心肺疾病）、免疫系统疾病或其他严重疾病（如精神疾病或认知障碍）的患者。</p>
              <p>2.社区研究对象：（1）不愿意参与研究，未能签署知情同意书者；（2）部分居住在工棚、部队等不便参与调查的居民；（3）合并有严重慢性疾病（如肿瘤、晚期心肺疾病）、免疫系统疾病或其他严重疾病（如精神疾病或认知障碍）的患者。</p>
              <p>（四）研究流程（筛选、入组、治疗方案、研究期间的检查项目、随访计划、标本收集方法及次数等）</p>
              <p><b>筛选：</b>北京、吉林、河北、浙江、湖北等省市的代表性学校（中学、高校）以及社区</p>
              <p><b>入组：</b>符合上述纳入标准的研究对象</p>
              <p><b>治疗方案：</b>本研究不提供或推荐任何药物治疗方案，仅对您既往治疗情况及随访期间的发病时间、严重程度、用药记录、就诊记录进行收集，以及对不同标签的个体展开多维度个性化的非药物干预并评估干预效果。您的药物治疗方案将由临床医师根据常规临床实践和现行指南自行决定。</p>
              <p><b>研究期间的检查项目：</b>风险筛查问卷、体格检查、血常规、总IgE、血清特异性IgE、皮肤点刺试验、区域环境数据采集，部分受试者进行常规肺功能及支气管舒张试验检查。</p>
              <p><b>随访计划：</b>队列完成基线调查后的半年内，将开展全体队列人员的首次随访，此次随访方式为现场调查。此后，每半年进行一次常规随访，其中中学和高校队列人员的随访主要通过数字化软件完成，而社区队列人员的随访仍以现场调查为主。随访内容涵盖以下关键信息：发病时间、症状严重程度、用药记录以及就诊记录等。在首次随访（即基线调查后半年）中，除上述常规内容外，还将同步采集区域环境数据，其操作流程与基线调查阶段保持一致。此外，若队列人员被纳入非药物治疗干预试验的干预组，则需在随访过程中进一步追踪干预措施的实施情况及其效果，以全面评估干预方案的有效性与可行性</p>
              <p><b>标本收集：</b></p>
              <p>1.生物样本：预计收集每位研究对象8mL血液（3ml全血及5ml血清）、10mL尿液（晨尿中段尿上清）以及1.5mL粪便样本。3ml血液样本（全血）用于检测血常规，5ml血液样本（血清）用于检测总IgE和血清特异性IgE，检测后的剩余样本留存于生物样本库。</p>
              <p>2.区域环境数据：为了解环境因素对哮喘及过敏性疾病的影响，需要采集区域环境数据，包括您所在学校/社区所在地区的室外环境数据（室外空气污染数据、花粉浓度）及室内环境数据（教室/宿舍/家中湿度、霉菌浓度、花粉浓度、可吸入颗粒物浓度）</p>
              <p>（五）抽样方法</p>
              <p>1.基线检查阶段：本研究将根据前期调查，在配合度更高且符合入组标准的研究现场，采用方便抽样的形式进行样本量的抽取和筛查。</p>
              <p>2.监测随访及数字疗法干预阶段：在进行数字疗法干预前，本研究将根据基线调查时为队列人群设立的标签特征对其进行分组，将具有相同标签特征的人群随机分配为对照组和干预组进行随机对照试验，通过长期的监测和随访，进而评价干预效果。</p>
              <p>（六）其他说明</p>
              <p>对于静脉采血检测后的剩余血液标本研究：静脉采血检测后的剩余血液标本将保存于生物样本库中，入组试验不会扩大您的采血数量。</p>
              <p>生物样本不仅限于本次研究使用：如拟用于未来研究可无需另外征得受试者同意、但必须再次报送伦理委员会同意后开展。拟用于未来研究的样本将保存在北京医院及当地合作团队具有国家人类遗传资源储藏资质的生物样本库中，进行规范化保藏和利用，且检测后的样本将进行销毁，其相关数据保存至北京医院信息系统平台并按规定使用。若在未来研究中发现有关您的重大健康问题，研究者将根据和遵循国家的有关程序要求，以适当的方式告知您。</p>
              <el-form-item key="p1" prop="p1" class="!mb-2">
                <el-radio-group v-model="form.p1" class="!flex !flex-col !items-start gap-2">
                  <el-radio label="同意用于未来研究" class="!mx-2"></el-radio><br>
                  <el-radio label="不同意用于未来研究" class="!mx-2"></el-radio>
                </el-radio-group>
              </el-form-item>
              <p><b>研究步骤</b></p>
              <div class="flex justify-center"><img class="w-full max-w-[800px]" src="./assets/images/AN0111.png" alt="研究步骤" /></div>
              <h4 class="font-bold">四、研究持续时间</h4>
              <p>本研究预计持续2年10个月（2025年3月-2027年12月）。在研究期间，我们会定期对您进行随访，随访时间为1年以上。</p>
              <h4 class="font-bold">五、参加研究的风险</h4>
              <p>1. 生物样本采集风险：在采集血液、尿液、粪便样本时，可能会出现短暂的不适感，如采血时的疼痛、头晕等，但这些不适通常会在短时间内缓解。对于血液采集，可能会出现局部淤血、感染等风险，但我们会严格遵守操作规范，尽量降低这些风险的发生概率。</p>
              <p>2. 隐私泄露风险：我们会严格遵守隐私保护规定，对您的个人信息进行保密处理。尽管如此，仍可能存在极小的隐私泄露风险，但我们会采取一切必要措施从以下方面注意保密：①在工作中将限定接触患者资料的人员；②在整理数据时屏蔽患者的个人信息；③在发表文章中注意保护您的个人隐私。</p>
              <p>3. 心理压力：参与本研究可能会让您对自己的健康状况产生一定的关注和焦虑。在本研究中，我们会为您提供专业的健康指导和支持，帮助您缓解心理压力</p>
              <h4 class="font-bold">六、参加研究的受益</h4>
              <p>如果您同意参加本研究，您将有可能获得直接的医疗受益：</p>
              <p>1. 健康监测与管理：通过参与本研究，您将获得健康监测和管理服务，包括体格检查、过敏原检测、肺功能检查等，有助于您更好地了解自己的健康状况，及时发现潜在的健康问题。</p>
              <p>2. 个性化干预措施：根据您的健康数据和疾病风险，我们将通过数字化平台为您提供个性化的非药物干预措施，如心理干预、饮食干预、运动干预等，帮助您改善生活方式，提高生活质量。</p>
              <p>您所参与本研究的数据将为进一步探索哮喘及过敏性疾病的流行病学特征、发生发展机制及影响因素提供帮助，为制定更为有效的疾病预防和治疗策略提供科学支撑，进而惠及更广泛的患者群体。本研究期望从您所提供的信息中获取洞见，以期未来能够使与您具有相似病情的患者群体受益。研究过程中若产生任何专利权或商业利益，相关权益将与您无直接关联。</p>
              <h4 class="font-bold">七、可选的其他医疗方案</h4>
              <p>如果不参加本研究，您仍然可以按照常规的医疗程序进行健康检查和疾病治疗。您可以选择到当地的医疗机构进行过敏原检测、肺功能检查等相关检查，根据检查结果进行相应的治疗和健康管理。但您将无法获得本研究提供的个性化的非药物干预措施和全程数字化管理服务。</p>
              <h4 class="font-bold">八、信息保密</h4>
              <p>我们会按照法律的要求为您的研究记录保密。我国的相关法律为隐私、数据和授权访问的安全提供了保障。当样本存储在北京医院或制备的研究材料提供（以科研为目的的样本转移）给其他研究者时，关于您的研究信息，我们会用一个独一无二的编号代表您，编码信息将被妥善存放在北京医院。在科学会议或者科学杂志上发表本研究获得的研究信息和数据时，您的身份将不会被公开。但为确保该研究符合相关法律法规要求，您的记录有可能被审阅。审阅者包括国家相关管理部门、中国医学科学院北京协和医学院伦理审查委员会公共卫生分委会。</p>
              <h4 class="font-bold">九、关于研究费用</h4>
              <p>参加本研究不会给您增加常规治疗/检查/操作之外的额外费用。</p>
              <h4 class="font-bold">十、补偿</h4>
              <p>本研究不会给您提供金钱补偿。</p>
              <h4 class="font-bold">十一、如果发生研究相关的损伤</h4>
              <p>如果您因参加研究而导致损伤，北京医院呼吸与危重症医学科会立刻提供必要的医疗护理，并遵照相应的法律法规，承担治疗的费用及相应的经济补偿。请联系主要佟训靓，手机号码18610021799。</p>
              <h4 class="font-bold">十二、拒绝参加或者退出研究</h4>
              <p>您有权在研究任何阶段自愿退出，无需提供理由。此决定不会受到任何形式的歧视或不利影响，您的医疗待遇和权益保持不变。如果您出现严重的不良反应，或者您的研究医生觉得继续参加研究不符合您的最佳利益，他/她会决定让您退出研究。如果发生该情况，我们将及时通知您，您的研究医生也会与您讨论您拥有的其他选择。如果医生认为突然中断试验会影响您的健康，可能会要求您在停止试验之前来医院进行一次检查。您中途退出后，今后将不收集与其有关的新数据。之前收集的研究数据可以纳入适当的统计分析，您的身份和研究数据依据知情同意书第八条得到相应的保密。此外，您还需归还在研究过程中收到的任何研究相关物品（如问卷、检测工具等）。</p>
              <h4 class="font-bold">十三、 相关咨询</h4>
              <p><b>（一）研究相关问题咨询</b></p>
              <p>在研究参与过程中，您若有任何与本研究相关的疑问，包括但不限于研究流程、干预措施、数据处理及隐私保护等，可随时联系研究团队的主要研究者或指定研究人员，以获取详细解答与必要支持。研究团队将确保您的问题得到及时、准确的回应，以保障您的知情权与参与权。联系方式如下：</p>
              <p>• 主要研究者：佟训靓，联系电话：18610021799，电子邮箱：<EMAIL></p>
              <p>• 吉林省研究现场负责人：崔巍巍，联系电话：13756048857，电子邮箱：<EMAIL></p>
              <p><b>（二）权益保护相关问题咨询</b></p>
              <p>若您在参与研究过程中，对自身权益保护存在任何疑问、不满或忧虑，包括但不限于隐私泄露、数据使用不当、研究过程中的不公正待遇等，可随时联系本研究的伦理审查机构——中国医学科学院北京协和医学院伦理审查委员会公共卫生分委会办公室，以维护自身合法权益。伦理审查机构将对您反映的问题进行严肃调查，并采取必要措施保障您的权益。联系方式如下：</p>
              <p>• 机构名称：中国医学科学院北京协和医学院伦理审查委员会公共卫生分委会</p>
              <p>• 联系电话：010-65120682</p>
              <div class="mt-4">
                <h4 class="font-bold text-center mb-4 text-xl leading-8">告知声明</h4>
                <p>“我已告知该受试者<b>哮喘及过敏性疾病筛查和管理研究</b>的背景、目的、步骤、风险及获益情况，给予他/她足够的时间阅读知情同意书、与他人讨论，并解答了其有关研究的问题；我已告知该受试者当遇到与研究相关的问题时可随时与<b>佟训靓教授</b>联系，遇到与自身权利/权益相关问题时随时与中国医学科学院北京协和医学院伦理审查委员会公共卫生分委会办公室联系，并提供了准确的联系方式；我已告知该受试者可以在任何时候、无需任何理由退出本研究；我已告知该受试者将得到这份知情同意书的副本，上面包含我和他/她的签名。”。</p>
                <div class="flex gap-4 my-8 md:flex-row flex-col">
                  <el-form-item key="p2" prop="p2" class="!mb-2 flex-1" label="获得知情同意的研究人员" :rules="ruleData.p2">
                    <el-input v-model="form.p2" class="!mx-2" placeholder="请输入"></el-input>
                  </el-form-item>
                  <el-form-item key="p3" prop="p3" class="!mb-2 flex-1" label="联系电话" :rules="ruleData.p3">
                    <el-input v-model="form.p3" class="!mx-2" placeholder="请输入"></el-input>
                  </el-form-item>
                  <el-form-item key="p4" prop="p4" class="!mb-2 flex-1" label="签署日期" :rules="ruleData.p4">
                    <el-date-picker v-model="form.p4" class="!mx-2 !w-full" placeholder="请输入"></el-date-picker>
                  </el-form-item>
                </div>
              </div>
              <div class="mt-4">
                <h4 class="font-bold text-center mb-4 text-xl leading-8">知情同意声明</h4>
                <p>“我已被告知<b>哮喘及过敏性疾病筛查和管理研究</b>的背景、目的、步骤、风险及获益情况。我有足够的时间和机会进行提问，问题的答复我很满意。我也被告知，当我有问题、不满、忧虑，或想进一步获得信息时，应当与谁联系。我已经阅读这份知情同意书，同意参加本研究，并承诺提供给研究者的资料信息、化验检查结果等均真实有效。我知道我可以在任何时候、无需任何理由退出本研究。我被告知我将得到这份知情同意书的副本，上面包含我和研究者的签名。”</p>
              </div>
              <div class="flex gap-4 my-8 mt-4 md:flex-row flex-col" v-if="isAdult">
                  <el-form-item key="p5" prop="p5" class="!mb-2 flex-1" label="受试者签名:" :rules="ruleData.p5">
                        <el-button v-if="signaturePad" type="primary" class="!mx-2" @click="signaturePad.open({
                            onConfirm:handleSignaturePadP5
                        })">点击上传签名</el-button>
                        <el-upload
                          v-else
                          :action="upload.url"
                          :headers="upload.headers"
                          :data="upload.data"
                          :on-success="(res)=>{form.p5 = res.data}"
                          :on-error="handleUploadError"
                          :on-remove="()=>{form.p5 = ''}"
                        >
                          <el-button type="primary" class="!mx-2">点击上传签名</el-button>
                        </el-upload>
                        <div class="flex items-center border border-gray-200 p-1.5 bg-white rounded-md" v-if="form.p5">
                            <img class="max-h-8" :src="form.p5" />
                        </div>
                  </el-form-item>
                  <el-form-item key="p6" prop="p6" class="!mb-2 flex-1" label="联系电话" :rules="ruleData.p6">
                    <el-input v-model="form.p6" class="!mx-2" placeholder="请输入"></el-input>
                  </el-form-item>
                  <el-form-item key="p7" prop="p7" class="!mb-2 flex-1" label="签署日期" :rules="ruleData.p7">
                    <el-date-picker v-model="form.p7" class="!mx-2 !w-full" placeholder="请输入"></el-date-picker>
                  </el-form-item>
              </div>
              <div class="mt-4" v-if="!isAdult">
                  <h4 class="font-bold mb-4 text-xl leading-8">【当受试者为未成年人或在受试者不能签字时被允许以下方式：】</h4>
                  <el-form-item key="p8" prop="p8" class="!mb-2 flex-1" label="法定代理人与受试者的关系：">
                      <el-input v-model="form.p8" class="!mx-2" placeholder="请输入"></el-input>
                  </el-form-item>
                  <div class="flex gap-4 my-8 flex-col md:flex-row">
                    <el-form-item key="p9" prop="p9" class="!mb-2 flex-1" label="法定代理人签字" :rules="ruleData.p9">
                      <el-button v-if="signaturePad" type="primary" class="!mx-2" @click="signaturePad.open({
                        onConfirm:handleSignaturePadP9,
                      })">点击上传签名</el-button>
                      <el-upload
                        v-else
                        :action="upload.url"
                        :headers="upload.headers"
                        :data="upload.data"
                        :on-success="(res)=>{form.p9 = res.data}"
                        :on-error="handleUploadError"
                        :on-remove="()=>{form.p9 = ''}"
                      >
                      <el-button type="primary" class="!mx-2">点击上传签名</el-button>
                    </el-upload>
                    </el-form-item>
                    <el-form-item key="p10" prop="p10" class="!mb-2 flex-1" label="联系电话" :rules="ruleData.p10">
                      <el-input v-model="form.p10" class="!mx-2" placeholder="请输入"></el-input>
                    </el-form-item>
                    <el-form-item key="p11" prop="p11" class="!mb-2 flex-1" label="签署日期" :rules="ruleData.p11">
                      <el-date-picker v-model="form.p11" class="!mx-2 !w-full" placeholder="请输入"></el-date-picker>
                    </el-form-item>
                  </div>
              </div>
            </div>
        </el-form>
    </div>
</template>

<script>
import _ from 'lodash'
import {getAccessToken} from "@/utils/auth";
export default {
    name: "AN0111",
    props: {
        formData: String,
        context: {
          type: Object,
          default: () => ({
              person: {},
              operator: {},
              env: {}
          })
        }
    },
    mounted() {
       if (this.formData) {
            this.form = JSON.parse(this.formData);
        }
    },
    computed: {
      isAdult() {
        return this.context.person.age >= 18;
      },
      signaturePad() {
        return this.context.signaturePad
      }
    },
    data() {
      const mobileReg = /^1[3-9]\d{9}$/;
      const validateMobile = (rule, value, callback) => {
        if(!mobileReg.test(value)) {
          callback(new Error('请输入正确的手机号'));
        }
        callback();
      };
        return {
          upload: {
            url: this.context.env.baseUrl + "/admin-api/infra/file/upload",
            headers: {Authorization: "Bearer " + getAccessToken()},
            data: {}
          },
          ruleData: {
            p2: [{ required: true, message: '请输入获得知情同意的研究人员', trigger: 'blur' }],
            p3: [{  validator: validateMobile}],
            p4: [{ required: true, message: '请输入签署日期', trigger: 'blur' }],
            p5: [{ required: true, message: '请输入受试者签名', trigger: 'blur' }],
            p6: [{  validator: validateMobile}],
            p7: [{ required: true, message: '请输入签署日期', trigger: 'blur' }],
            p8: [{ required: true, message: '请输入法定代理人与受试者的关系', trigger: 'blur' }],
            p9: [{ required: true, message: '请输入法定代理人签字', trigger: 'blur' }],
            p10: [{  validator: validateMobile}],
            p11: [{ required: true, message: '请输入签署日期', trigger: 'blur' }],
          },
          form: {
            p1: null,
            p2: this.context.operator.nickname,
            p3: this.context.operator.mobile,
            p4: new Date().toLocaleDateString(),
            p5: '',
            p6: this.context.person.mobile,
            p7: new Date().toLocaleDateString(),
            p8: '',
            p9: '',
            p10: '',
            p11: new Date().toLocaleDateString(),
          },
        };
    },
    methods: {
      getData() {
          return this.form;
      },
      validateData() {
          return new Promise((resolve) => {
            this.$refs["form"].validate((valid) => {
                resolve(valid);
            });
          });
      },
      handleUploadError(error, file, fileList) {
        this.$modal.msgError("上传失败");
      },

      handleSignaturePadP5(imageUrl) {
        console.log('handleSignaturePadP5')
        console.log(imageUrl)
        this.form.p5 = imageUrl
        console.log(this.form.p5)
      },
      handleSignaturePadP9(imageUrl) {
        this.form.p9 = imageUrl
        console.log(this.form.p9)
      }
    },
};
</script>
<style scoped>
</style>
