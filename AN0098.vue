<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            label-position="top"
            size="mini"
        >
            <el-form-item v-for="item in formList" :key="item.key" :prop="item.key" :label="item.label" :rules="item.rules">
                <el-radio-group v-model="form[item.key]">
                    <el-radio v-for="label in item.options" :key="label" :label="label" :value="label" class="!m-2" border></el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
const formList = [{
  label: '在过去 4周内，在工作、学习或家中多少时候哮喘妨碍您进行日常活动？',
  key: 'p1',
  options: ['所有时间', '大多数时间', '有些时间', '极少时间', '没有'],
  rules: [{
    required: true,
    message: '请选择',
    trigger: 'blur'
  }]
}, {
  label: '2、在过去4周内，您有多少次呼吸困难？',
  key: 'p2',
  options: ['每天不止1次', '每天1次', '每周3~6次', '每周1~2次', '完全没有'],
  rules: [{
    required: true,
    message: '请选择',
    trigger: 'blur'
  }]
}, {
  label: '3、在过去4周内，因为哮喘症状（喘息、咳嗽、呼吸困难、胸闷或疼痛），您有多少次在夜间醒来或早上比平时早醒？',
  key: 'p3',
  options: ['每周4个晚上或更多', '每周2~3个晚上', '每周1次', '1~2次', '没有'],
  rules: [{
    required: true,
    message: '请选择',
    trigger: 'blur'
  }]
}, {
  label: '4、过去 4 周内，您有多少次使用急救药物治疗（如沙丁胺醇）？',
  key: 'p4',
  options: ['每天3次以上', '每天1~2次', '每周2~3次', '每周1次或更少', '没有'],
  rules: [{
    required: true,
    message: '请选择',
    trigger: 'blur'
  }]
}, {
  label: '5、您如何评估过去4周内您的哮喘控制情况？',
  key: 'p5',
  options: ['没有控制', '控制很差', '有所控制', '良好控制', '完全控制'],
  rules: [{
    required: true,
    message: '请选择',
    trigger: 'blur'
  }]
} ]
export default {
    name: "AN0098",
    props: {
        formData: String,
        context: {
          type: Object,
          default: () => ({
              person: {}
          })
        }
    },
    mounted() {
       if (this.formData) {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            formList,
            form: {
              p1: null,
              p2: null,
              p3: null,
              p4: null,
              p5: null,
            },
        };
    },
    methods: {
      getData() {
          return this.form;
      },
      validateData() {
          return new Promise((resolve) => {
            this.$refs["form"].validate((valid) => {
                resolve(valid);
            });
          });
      },
    },
};

export const formConfig = {
  fieldList: [{
    label: '在过去 4周内，在工作、学习或家中多少时候哮喘妨碍您进行日常活动？',
    key: 'p1',
  }, {
    label: '2、在过去4周内，您有多少次呼吸困难？',
    key: 'p2',
  }, {
    label: '3、在过去4周内，因为哮喘症状（喘息、咳嗽、呼吸困难、胸闷或疼痛），您有多少次在夜间醒来或早上比平时早醒？',
    key: 'p3',
  }, {
    label: '4、过去 4 周内，您有多少次使用急救药物治疗（如沙丁胺醇）？',
    key: 'p4',
  }, {
    label: '5、您如何评估过去4周内您的哮喘控制情况？',
    key: 'p5',
  } ]
}
</script>
<style scoped>
</style>
