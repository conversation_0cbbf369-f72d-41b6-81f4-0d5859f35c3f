<template>
<div class="flex flex-col w-full">
    <div class="w-full flex justify-between">
        <slot name="left">
            <span>{{ leftText }}</span>
        </slot>
        <slot name="right">
            <span>{{ rightText }}</span>
        </slot>
    </div>
    <div class="w-full flex items-center border border-l-0 border-gray-300 bg-base-100">
        <div
            v-for="(item, index) in showList"
            class="flex-1 text-center text-gray-500 py-1 border-l border-gray-300 cursor-pointer"
            :class="{'bg-gray-200': current >= item}"
            :key="index"
            @click="clickNode(item)"
        >
            {{ item }}
        </div>
    </div>
</div>
</template>
<script>
export default {
  props: {
    value: {
      type: Number,
      default: 0
    },
    leftText: {
      type: String,
    },
    rightText: {
      type: String,
    },
    step: {
      type: Number,
      default: 1
    },
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 10
    }
  },
  computed: {
    showList(){
      return Array.from({length: this.max - this.min + 1}, (_, index) => this.min + index * this.step);
    }
  },
  data(){
    return {
      current: this.value
    }
  },
  methods: {
    clickNode(item){
      this.current = item;
      this.$emit('input', item);
    }
  }
};
</script>
