<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            label-position="top"
            size="mini"
        >
            <template v-for="item in formList">
                <el-form-item :key="item.key" :prop="item.key" :label="item.label" :rules="item.rules" v-if="!item?.isHidden">
                    <el-radio-group v-if="item.type === 'radio'" v-model="form[item.key]" @change="item.handleChange&&item.handleChange()">
                        <el-radio v-for="label in item.options" :key="label" :label="label" :value="label" class="!m-2" border></el-radio>
                    </el-radio-group>
                    <el-checkbox-group  v-if="item.type === 'checkbox'" v-model="form[item.key]" @change="item.handleChange&&item.handleChange()">
                      <el-checkbox v-for="label in item.options" :key="label" :label="label" :value="label" class="!m-2" border></el-checkbox>
                    </el-checkbox-group>
                    <el-input v-model="form[`${item.key}_input`]" class="!m-2" v-if="item.showInput" placeholder="请输入"></el-input>
                    <Slider :value="form[item.key]" @input="(val) => form[item.key] = val" v-if="item.type === 'slider'" :min="1" :max="10" :step="1" />
                </el-form-item>
            </template>
        </el-form>
    </div>
</template>

<script>
import Slider from './components/Slider.vue';
import _ from 'lodash'
export default {
    name: "AN0109",
    components: {
      Slider
    },
    props: {
        formData: String,
        context: {
          type: Object,
          default: () => ({
              person: {}
          })
        }
    },
    mounted() {
       if (this.formData) {
            this.form = JSON.parse(this.formData);
        }
    },
    computed: {
      formList(){
        return [{
          label: '1、在过去12个月内，除感冒或流感外，您是否出现过以下鼻部症状？（多选）',
          key: 'p1',
          type: 'checkbox',
          options: ['连续打喷嚏', '流清涕', '鼻塞', '鼻痒', '不存在以上症状'],
          handleChange:(key) => {
            if(this.form.p1.includes('不存在以上症状')){
              this.form.p1 = ['不存在以上症状'];
              this.form.p1_1 = null;
              this.form.p1_2 = [];
              this.form.p1_3 = [];
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.1、在过去的12个月里，这种鼻部症状是否伴随过眼睛发痒流泪?',
          key: 'p1_1',
          type: 'radio',
          isHidden: this.form.p1.length === 0 || _.includes(this.form.p1, '不存在以上症状'),
          options: ['是', '否'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        },
        {
          label: '1.2、在过去的12个月里的哪个月，您出现了以上鼻部问题?（多选）',
          key: 'p1_2',
          type: 'checkbox',
          isHidden: this.form.p1.length === 0 || _.includes(this.form.p1, '不存在以上症状'),
          options: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.3、什么触发因素会引发或加剧您的鼻部问题?（多选）',
          key: 'p1_3',
          type: 'checkbox',
          options: ['花粉', '房屋灰尘', '房屋尘螨', '动物（猫、狗等）', '冷空气', '不详', '其他'],
          isHidden: this.form.p1.length === 0 || _.includes(this.form.p1, '不存在以上症状'),
          showInput: this.form.p1_3.includes('其他'),
          handleChange: () => {
            if(this.form.p1_3.includes('其他')) {
              this.form.p1_3 = ['其他'];
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '2、您是否曾反复出现眼睛发痒或流泪？',
          key: 'p2',
          type: 'radio',
          options: ['是', '否'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '2.1、 在过去的12个月里的哪个月，您出现了眼睛发痒或流泪的问题?（多选）',
          key: 'p2_1',
          type: 'checkbox',
          isHidden: this.form.p2 !== '是',
          options: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '2.2、当您眼睛发痒或流泪时，您是否同时也存在结膜充血?（白眼球有很多红血丝）',
          key: 'p2_2',
          type: 'radio',
          isHidden: this.form.p2 !== '是',
          options: ['是', '否'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '2.3、 什么触发因素会引发或加剧您眼睛发痒或流泪的问题?（多选）',
          key: 'p2_3',
          type: 'checkbox',
          isHidden: this.form.p2 !== '是',
          options: ['花粉', '房屋灰尘', '房屋尘螨', '动物（猫、狗等）', '冷空气', '不详', '其他'],
          showInput: this.form.p2_3.includes('其他'),
          handleChange: () => {
            if(this.form.p2_3.includes('其他')) {
              this.form.p2_3 = ['其他'];
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '2.4、过去的12个月内，您有多少次感到眼睛发痒或流泪?（眼睛发痒或流泪从出现到完全消失或日常无症状状态被视为一次发作;若症状完全缓解后，再次暴露于过敏原并出现眼睛发痒或流泪，则视为新一次发作）',
          key: 'p2_4',
          type: 'radio',
          isHidden: this.form.p2 !== '是',
          options: ['未发生', '1-4次', '5-10次', '10次以上'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '2.5、过去的12个月里，您自我感觉因眼部过敏症状所带来的不适感严重程度如何？（最左端为0，表示您不受过敏症状的困扰；最右端为10，表示过敏症状极其困扰您）',
          key: 'p2_5',
          type: 'slider',
          isHidden: this.form.p2 !== '是',
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '3、您是否认为自己对某种物质过敏?',
          key: 'p3',
          type: 'radio',
          options: ['是', '否'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '3.1、您认为自己对何种物质过敏？（多选）',
          key: 'p3_1',
          type: 'checkbox',
          isHidden: this.form.p3 !== '是',
          options: ['尘螨', '花粉', '宠物毛发', '霉菌', '食物（如牛奶、鸡蛋白、海鲜等）', '其他'],
          showInput: this.form.p3_1.includes('其他'),
          handleChange: () => {
            if(this.form.p3_1.includes('其他')) {
              this.form.p3_1 = ['其他'];
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '4、您是否已经做过过敏测试，如皮肤点刺试验（SPT）、免疫球蛋白E检测（IgE）? ',
          key: 'p4',
          type: 'radio',
          options: ['是', '否'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '4.1、过敏测试结果如何？',
          key: 'p4_1',
          type: 'radio',
          isHidden: this.form.p4 !== '是',
          options: ['阳性', '阴性'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '4.2、过敏测试阳性，提示您对何种物质过敏？（多选）',
          key: 'p4_2',
          type: 'checkbox',
          isHidden: this.form.p4 !== '是',
          options: ['尘螨', '花粉', '宠物毛发', '霉菌', '食物（如牛奶、鸡蛋白、海鲜等）', '其他'],
          showInput: this.form.p4_2.includes('其他'),
          handleChange: () => {
            if(this.form.p4_2.includes('其他')) {
              this.form.p4_2 = ['其他'];
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '4.3、您对花粉过敏，具体的花粉种类是什么？（多选）',
          key: 'p4_3',
          type: 'checkbox',
          isHidden: this.form.p4 !== '是',
          options: ['柏树', '柳树', '杨树', '榆树', '桦树', '梧桐树', '白蜡树', '葎草', '蒿草', '䐁草', '藜', '具体不明', '其他'],
          showInput: this.form.p4_3.includes('其他'),
          handleChange: () => {
            if(this.form.p4_3.includes('其他')) {
              this.form.p4_3 = ['其他'];
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '5、您是否被医生诊断患有过敏性疾病？',
          key: 'p5',
          type: 'radio',
          options: ['是', '否'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '5.1、您被诊断患何种疾病？（多选）',
          key: 'p5_1',
          type: 'checkbox',
          options: ['无', '特应性皮炎', '过敏性鼻炎', '哮喘', '食物过敏(含消除饮食)', '过敏性结膜炎', '荨麻疹', '过敏性休克', '其他'],
          isHidden: this.form.p5 !== '是',
          showInput: this.form.p5_1.includes('其他'),
          handleChange: () => {
            if(this.form.p5_1.includes('其他')) {
              this.form.p5_1 = ['其他'];
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '6、您的家人是否患有过敏性疾病?',
          key: 'p6',
          type: 'radio',
          options: ['是', '否'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '6.1、在您的父亲及父亲一系亲属（包括爷爷，奶奶，姑姑，叔叔等）中是否有患有以下过敏性疾病？',
          key: 'p6_1',
          type: 'checkbox',
          isHidden: this.form.p6 !== '是',
          options: ['无', '特应性皮炎', '过敏性鼻炎', '哮喘', '食物过敏(含消除饮食)', '过敏性结膜炎', '荨麻疹', '过敏性休克', '其他'],
          showInput: this.form.p6_1.includes('其他'),
          handleChange: () => {
            if(this.form.p6_1.includes('无')) {
              this.form.p6_1 = ['无'];
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '6.2、在您的母亲及母亲一系亲属（包括爷爷，奶奶，姑姑，叔叔等）中是否有患有以下过敏性疾病？',
          key: 'p6_2',
          type: 'checkbox',
          isHidden: this.form.p6 !== '是',
          options: ['无', '特应性皮炎', '过敏性鼻炎', '哮喘', '食物过敏(含消除饮食)', '过敏性结膜炎', '荨麻疹', '过敏性休克', '其他'],
          showInput: this.form.p6_2.includes('其他'),
          handleChange: () => {
            if(this.form.p6_2.includes('无')) {
              this.form.p6_2 = ['无'];
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '6.3、在您的兄弟姐妹中是否有患有以下过敏性疾病？',
          key: 'p6_3',
          type: 'checkbox',
          isHidden: this.form.p6 !== '是',
          options: ['无', '特应性皮炎', '过敏性鼻炎', '哮喘', '食物过敏(含消除饮食)', '过敏性结膜炎', '荨麻疹', '过敏性休克', '其他'],
          showInput: this.form.p6_3.includes('其他'),
          handleChange: () => {
            if(this.form.p6_3.includes('无')) {
              this.form.p6_3 = ['无'];
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '7、您是否接受过早期抗生素治疗（即在2岁以下时应用过抗生素）？',
          key: 'p7',
          type: 'radio',
          options: ['是', '否', '不详'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '8、您是否在婴儿时期接受过超过6个月的母乳喂养（含混合喂养）？',
          key: 'p8',
          type: 'radio',
          options: ['是', '否', '不详'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '9、您的出生方式是否为剖腹产？',
          key: 'p9',
          type: 'radio',
          options: ['是', '否'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        },
      ]
      }
    },
    data() {
        return {
          form: {
            p1: [],
            p1_1: '',
            p1_2: [],
            p1_3: [],
            p1_3_input: '',
            p2: '',
            p2_1: [],
            p2_2: '',
            p2_3: [],
            p2_3_input: '',
            p2_4: '',
            p2_5: '',
            p3: '',
            p3_1: [],
            p3_1_input: '',
            p4: '',
            p4_1: '',
            p4_2: [],
            p4_2_input: '',
            p4_3: [],
            p5: '',
            p5_1: [],
            p5_1_input: '',
            p6: '',
            p6_1: [],
            p6_1_input: '',
            p6_2: [],
            p6_2_input: '',
            p6_3: [],
            p6_3_input: '',
            p7: '',
            p8: '',
            p9: '',
          },
        };
    },
    methods: {
      getData() {
          return this.form;
      },
      validateData() {
          return new Promise((resolve) => {
            this.$refs["form"].validate((valid) => {
                resolve(valid);
            });
          });
      },
    },
};

export const formConfig = {
  fieldList: [{
      label: '1、在过去12个月内，除感冒或流感外，您是否出现过以下鼻部症状？（多选）',
      key: 'p1',
      type: 'checkbox',
    }, {
      label: '1.1、在过去的12个月里，这种鼻部症状是否伴随过眼睛发痒流泪?',
      key: 'p1_1',
      type: 'radio',
    },
    {
      label: '1.2、在过去的12个月里的哪个月，您出现了以上鼻部问题?（多选）',
      key: 'p1_2',
      type: 'checkbox',
    }, {
      label: '1.3、什么触发因素会引发或加剧您的鼻部问题?（多选）',
      key: 'p1_3',
      type: 'checkbox',
    }, {
      label: '2、您是否曾反复出现眼睛发痒或流泪？',
      key: 'p2',
      type: 'radio',
    }, {
      label: '2.1、 在过去的12个月里的哪个月，您出现了眼睛发痒或流泪的问题?（多选）',
      key: 'p2_1',
      type: 'checkbox',
    }, {
      label: '2.2、当您眼睛发痒或流泪时，您是否同时也存在结膜充血?（白眼球有很多红血丝）',
      key: 'p2_2',
      type: 'radio',
    }, {
      label: '2.3、 什么触发因素会引发或加剧您眼睛发痒或流泪的问题?（多选）',
      key: 'p2_3',
      type: 'checkbox',
    }, {
      label: '2.4、过去的12个月内，您有多少次感到眼睛发痒或流泪?（眼睛发痒或流泪从出现到完全消失或日常无症状状态被视为一次发作;若症状完全缓解后，再次暴露于过敏原并出现眼睛发痒或流泪，则视为新一次发作）',
      key: 'p2_4',
      type: 'radio',
    }, {
      label: '2.5、过去的12个月里，您自我感觉因眼部过敏症状所带来的不适感严重程度如何？（最左端为0，表示您不受过敏症状的困扰；最右端为10，表示过敏症状极其困扰您）',
      key: 'p2_5',
      type: 'slider',
    }, {
      label: '3、您是否认为自己对某种物质过敏?',
      key: 'p3',
      type: 'radio',
    }, {
      label: '3.1、您认为自己对何种物质过敏？（多选）',
      key: 'p3_1',
      type: 'checkbox',
    }, {
      label: '4、您是否已经做过过敏测试，如皮肤点刺试验（SPT）、免疫球蛋白E检测（IgE）? ',
      key: 'p4',
      type: 'radio',
    }, {
      label: '4.1、过敏测试结果如何？',
      key: 'p4_1',
      type: 'radio',
    }, {
      label: '4.2、过敏测试阳性，提示您对何种物质过敏？（多选）',
      key: 'p4_2',
      type: 'checkbox',
    }, {
      label: '4.3、您对花粉过敏，具体的花粉种类是什么？（多选）',
      key: 'p4_3',
      type: 'checkbox',
    }, {
      label: '5、您是否被医生诊断患有过敏性疾病？',
      key: 'p5',
      type: 'radio',
    }, {
      label: '5.1、您被诊断患何种疾病？（多选）',
      key: 'p5_1',
      type: 'checkbox',
    }, {
      label: '6、您的家人是否患有过敏性疾病?',
      key: 'p6',
      type: 'radio',
    }, {
      label: '6.1、在您的父亲及父亲一系亲属（包括爷爷，奶奶，姑姑，叔叔等）中是否有患有以下过敏性疾病？',
      key: 'p6_1',
      type: 'checkbox',
    }, {
      label: '6.2、在您的母亲及母亲一系亲属（包括爷爷，奶奶，姑姑，叔叔等）中是否有患有以下过敏性疾病？',
      key: 'p6_2',
      type: 'checkbox',
    }, {
      label: '6.3、在您的兄弟姐妹中是否有患有以下过敏性疾病？',
      key: 'p6_3',
      type: 'checkbox',
    }, {
      label: '7、您是否接受过早期抗生素治疗（即在2岁以下时应用过抗生素）？',
      key: 'p7',
      type: 'radio',
    }, {
      label: '8、您是否在婴儿时期接受过超过6个月的母乳喂养（含混合喂养）？',
      key: 'p8',
      type: 'radio',
    }, {
      label: '9、您的出生方式是否为剖腹产？',
      key: 'p9',
      type: 'radio',
    },
  ]
}
</script>
<style scoped>
</style>
