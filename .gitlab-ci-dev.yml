variables:
  NODE_ENV: 'production'
  DOCKER_IMAGE_NAME: "iris-web"
  SERVER_USER: "root"
  DOCKER_TLS_CERTDIR: "/certs"

build_job_dev:
  tags:
    - dev
  stage: build
  only:
    - develop
  image: node:20 # 使用 Node.js 16 的 Docker 镜像
  before_script:
    - fallocate -l 2G /swapfile || true
    - chmod 600 /swapfile || true
    - mkswap /swapfile || true
    - swapon /swapfile || true
  script:
    - npm run init:submodule
    - npm cache clean --force  # 清除 npm 缓存
    - rm -rf node_modules
    - yarn install --production=false # 安装依赖
    - mv ./nginx.stage.conf ./nginx.conf
    - npm run build:stage # 构建项目

  artifacts:
    paths:
      - dist/ # 假设构建输出到 dist 目录
      - nginx.conf
      - Dockerfile

docker_job_dev:
  tags:
    - dev
  stage: docker
  only:
    - develop
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  script:
    - pwd  # 显示当前工作目录
    - echo "Listing current directory contents:"
    - ls -la  # 列出所有文件
    - if [ -d "dist" ]; then echo "dist exists"; else echo "dist does not exist"; exit 1; fi
    - docker build -t iris-web .
    - echo $DOCKER_PASSWORD | docker login --username=petezhang --password-stdin crpi-9dfdvx267ioh5678.cn-hangzhou.personal.cr.aliyuncs.com
    - docker tag iris-web crpi-9dfdvx267ioh5678.cn-hangzhou.personal.cr.aliyuncs.com/iris-platform/iris-web-dev:$CI_PIPELINE_IID
    - docker push crpi-9dfdvx267ioh5678.cn-hangzhou.personal.cr.aliyuncs.com/iris-platform/iris-web-dev:$CI_PIPELINE_IID
  dependencies:
    - build_job_dev

deploy_job_dev:
  stage: deploy
  variables:
    SERVER_HOST: "***********"
  tags:
    - dev
  only:
    - develop
  image: alpine:latest
  before_script:
    - apk --repository https://mirrors.tuna.tsinghua.edu.cn/alpine/v3.14/main add openssh-client sshpass
  script:
    - sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_HOST
        "
        echo $DOCKER_PASSWORD | docker login --username=petezhang --password-stdin crpi-9dfdvx267ioh5678.cn-hangzhou.personal.cr.aliyuncs.com &&
        docker pull crpi-9dfdvx267ioh5678.cn-hangzhou.personal.cr.aliyuncs.com/iris-platform/iris-web-dev:$CI_PIPELINE_IID &&
        docker stop iris-web-dev || true &&
        docker rm iris-web-dev || true &&
        docker run -d --name iris-web-dev -p 8081:8081 crpi-9dfdvx267ioh5678.cn-hangzhou.personal.cr.aliyuncs.com/iris-platform/iris-web-dev:$CI_PIPELINE_IID
        "
  dependencies:
    - docker_job_dev
