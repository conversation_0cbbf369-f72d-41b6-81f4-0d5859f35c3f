<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            label-position="top"
            size="mini"
        >
          <template v-for="item in formList">
              <h3 class="font-bold mb-4 text-2xl leading-8" v-if="item.type === 'header'">{{ item.label }}</h3>
              <h4 class="font-bold mb-4 text-xl leading-8" v-else-if="item.type === 'sub-header'">{{ item.label }}</h4>
              <template v-else>
                <el-form-item :key="item.key" :prop="item.key" :label="item.label" :rules="item.rules" v-if="!item?.isHidden">
                    <el-radio-group v-if="item.type === 'radio'" v-model="form[item.key]" @change="item.handleChange&&item.handleChange()">
                        <el-radio v-for="label in item.options" :key="label" :label="label" :value="label" class="!m-2" border></el-radio>
                    </el-radio-group>
                    <el-checkbox-group  v-if="item.type === 'checkbox'" v-model="form[item.key]" @change="item.handleChange&&item.handleChange()">
                      <el-checkbox v-for="label in item.options" :key="label" :label="label" :value="label" class="!m-2" border></el-checkbox>
                    </el-checkbox-group>
                    <el-input v-model="form[`${item.key}_input`]" class="!m-2" v-if="item.showInput" placeholder="请输入" @change="item.handleChange&&item.handleChange()"></el-input>
                    <el-input v-if="item.type === 'input-number'" type="number" :disabled="item.disabled" v-model="form[item.key]" class="!m-2" :min="item.min" :max="item.max" :step="item.step" :unit="item.unit" placeholder="请输入" @change="item.handleChange&&item.handleChange()">
                      <template v-if="item.unit" #append>
                        <span>{{ item.unit }}</span>
                      </template>
                    </el-input>
                    <el-input v-if="item.type === 'input-text'" v-model="form[item.key]" class="!m-2" placeholder="请输入" @change="item.handleChange&&item.handleChange()"></el-input>
                    <el-date-picker v-if="item.type === 'input-date-day'" v-model="form[item.key]" class="!m-2" type="date" placeholder="请选择" @change="item.handleChange&&item.handleChange()"></el-date-picker>
                    <el-upload
                      v-if="item.type === 'upload'"
                      :action="upload.url"
                      :headers="upload.headers"
                      :data="upload.data"
                      :on-success="item.handleUploadSuccess"
                      :on-error="handleUploadError"
                      :on-remove="item.handleRemove"
                    >
                      <el-button type="primary" class="!mx-2">点击上传</el-button>
                    </el-upload>
                    <div class="flex items-center border border-gray-200 p-1.5 bg-white rounded-md" v-if="item.type === 'upload' && form[item.key]">
                        <img class="max-h-8" :src="form[item.key]" />
                    </div>
                    <el-cascader
                      v-if="item.type === 'address-select'"
                      size="large"
                      :options="pcTextArr"
                      v-model="form[item.key]">
                    </el-cascader>
                </el-form-item>
              </template>
            </template>
        </el-form>
    </div>
</template>

<script>
import {
  pcTextArr,
} from "element-china-area-data";
import _ from 'lodash'
import { getAccessToken } from '@/utils/auth'
export default {
    name: "AN0112",
    props: {
        formData: String,
        context: {
          type: Object,
          default: () => ({
              person: {},
              operator: {},
              env: {}
          })
        }
    },
    mounted() {
       if (this.formData) {
            this.form = JSON.parse(this.formData);
        }
    },
    computed: {
      formList(){
        return [{
          label: '1、个人及家庭信息',
          type: 'header'
        },{
          label: '1、性别',
          key: 'p1',
          type: 'radio',
          disabled: true,
          options: ['男', '女'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '2、年龄',
          key: 'p2',
          type: 'input-number',
          unit: '岁',
          min: 0,
          max: 100,
          step: 1,
          disabled: true,
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        },
        {
          label: '3、籍贯',
          key: 'p3',
          type: 'address-select',
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '4、生源地',
          key: 'p4',
          type: 'address-select',
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        },{
          label: '5、烟草使用',
          type: 'sub-header',
        }, {
          label: '5.1、吸烟状态',
          key: 'p5',
          type: 'checkbox',
          options: ['从不吸烟（出生起至今）', '长期戒烟（≥5年）', '短期戒烟（<5年）', '短期复吸者（戒烟后复吸<5年）', '长期复吸者（戒烟后复吸≥5年）', '持续吸烟者（吸烟超过10年且仍吸烟）'],
          handleChange: () => {
            if( this.form.p5.includes('从不吸烟（出生起至今）')) {
              this.form.p5 = ['从不吸烟（出生起至今）'];
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        },{
          label: '5.2.1、每天吸烟支数',
          key: 'p5_2_1',
          type: 'input-number',
          unit: '支',
          isHidden: this.form.p5.includes('从不吸烟（出生起至今）'),
          min: 1,
          max: 100,
          step: 1,
          handleChange: () => {
            if(this.form.p5_2_1 * this.form.p5_2_2 > 0) {
              this.form.p5_2_3 = this.form.p5_2_1 * this.form.p5_2_2;
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '5.2.2、吸烟年数',
          key: 'p5_2_2',
          type: 'input-number',
          unit: '年',
          isHidden: this.form.p5.includes('从不吸烟（出生起至今）'),
          min: 1,
          max: 100,
          step: 1,
          handleChange: () => {
            if(this.form.p5_2_1 * this.form.p5_2_2 > 0) {
              this.form.p5_2_3 = this.form.p5_2_1 * this.form.p5_2_2;
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        },{
          label: '5.2.3、吸烟指数',
          key: 'p5_2_3',
          type: 'input-number',
          isHidden: this.form.p5.includes('从不吸烟（出生起至今）'),
          step: 1,
          disabled: true,
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '6、酒精使用',
          type: 'sub-header',
        }, {
          label: '6.1、过去一年饮酒频率',
          key: 'p6_1',
          type: 'radio',
          options: ['从未喝过', '每月1次或不到1次', '每月2-4次', '每周4次或更多'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '6.1.1、饮酒类型（多选）',
          key: 'p6_1_1',
          type: 'checkbox',
          isHidden: this.form.p6_1 == '从未喝过',
          options: ['红酒', '白酒', '啤酒', '洋酒（威士忌、伏特加等）', '其他'],
          showInput: this.form.p6_1_1.includes('其他'),
          handleChange: () => {
            if( this.form.p6_1_1.includes('其他')) {
              this.form.p6_1_1 = ['其他'];
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '6.1.2、饮酒度数',
          key: 'p6_1_2',
          type: 'input-number',
          isHidden: this.form.p6_1 == '从未喝过',
          step: 1,
          min: 1,
          max: 100,
          unit: '% vol.',
          handleChange: () => {
            if(this.form.p6_1_3 && this.form.p6_1_2) {
              this.form.p6_1_4 = 0.8 * this.form.p6_1_3 * this.form.p6_1_2 / 100;
            }
          },
          rules: [{
            required: true,
            message: '请输入',
            trigger: 'blur'
          }]
        },{
          label: '6.1.3、平均每次饮酒量',
          key: 'p6_1_3',
          type: 'input-number',
          isHidden: this.form.p6_1 == '从未喝过',
          step: 1,
          min: 1,
          max: 10000,
          unit: 'ml',
          handleChange: () => {
            if(this.form.p6_1_3 && this.form.p6_1_2) {
              this.form.p6_1_4 = 0.8 * this.form.p6_1_3 * this.form.p6_1_2 / 100;
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        },{
          label: '6.1.4、酒精量',
          key: 'p6_1_4',
          type: 'input-number',
          isHidden: this.form.p6_1 == '从未喝过',
          step: 1,
          disabled: true,
          unit: 'g',
          rules: [{
            required: true,
            message: '请输入',
            trigger: 'blur'
          }]
        }, {
          label: '7、运动情况',
          type: 'sub-header',
        }, {
          label: '7.1、过去一年运动类型（多选）',
          key: 'p7_1',
          type: 'checkbox',
          options: ['有氧运动（快走、慢跑、骑自行车、做广播体操和有氧健身操、登山、爬楼梯等）', '力量练习（俯卧撑、哑铃等）', '团队运动（足球、篮球等）', '综合训练（跳舞、瑜伽、普拉提、太极拳等）'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '7.2、过去一年运动频率',
          key: 'p7_2',
          type: 'input-number',
          step: 1,
          min: 0,
          max: 7,
          unit: '天/周',
          rules: [{
            required: true,
            message: '请输入',
            trigger: 'blur'
          }]
        }, {
          label: '7.3、过去一年运动强度',
          key: 'p7_3',
          type: 'radio',
          options: ['轻度', '中度', '剧烈'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        },{
          label: '7.4、过去一年每次运动持续时间',
          key: 'p7_4',
          type: 'radio',
          options: ['<20分钟', '20~30分钟', '30~60分钟', '>60分钟'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '8、膳食行为',
          type: 'sub-header',
        },{
          label: '8.1、饮食偏好（多选）',
          key: 'p8_1',
          type: 'checkbox',
          options: ['嗜甜', '嗜咸', '嗜酸', '嗜食辛辣', '喜油腻', '清淡饮食'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '8.2、饮食结构',
          key: 'p8_2',
          type: 'radio',
          options: ['动物性食物为主型（膳食组成以动物性食物为主，膳食营养组成特点为高能量、高蛋白质、高脂肪、低膳食纤维）', '植物性食物为主型（膳食组成以植物性食物为主，动物性食物较少）', '动植物性食物结合型（膳食中植物性和动物性食物构成比例适宜，优质蛋白质约占膳食蛋白质的50%以上）'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '8.3、新鲜蔬菜水果摄入量',
          key: 'p8_3',
          type: 'radio',
          options: ['<500g/天', '≥500g/天'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '9、环境暴露',
          type: 'sub-header',
        }, {
          label: '9.1、二手烟暴露情况',
          key: 'p9_1',
          type: 'radio',
          options: ['无暴露（过去一年内未接触任何环境烟草烟雾）', '偶尔暴露（平均每周暴露1~2天）', '经常暴露（平均每周暴露3~4天）', '持续暴露（平均每周暴露≥5天）'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '9.2、宠物是否饲养宠物',
          key: 'p9_2',
          type: 'radio',
          options: ['是', '否'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '9.2.1、宠物种类',
          key: 'p9_2_1',
          type: 'input-text',
          rules: [{
            required: true,
            message: '请输入',
            trigger: 'blur'
          }]
        },{
          label: '9.2.2、饲养宠物时间',
          key: 'p9_2_2',
          type: 'input-date-day',
          rules: [{
            required: true,
            message: '请输入',
            trigger: 'blur'
          }]
        }, {
          label: '10、疾病史（多选）',
          key: 'p10',
          type: 'checkbox',
          options: ['无', '超重/肥胖', '失眠', '焦虑', '抑郁', '其他'],
          showInput: this.form.p10.includes('其他'),
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        },{
          label: '11、药物史',
          key: 'p11',
          type: 'input-text',
          rules: [{
            required: true,
            message: '请输入',
            trigger: 'blur'
          }]
        }, {
          label: '12、家庭经济情况',
          type: 'sub-header',
        }, {
          label: '12.1、家庭可支配收入',
          key: 'p12_1',
          type: 'input-number',
          unit: '元/月',
          rules: [{
            required: true,
            message: '请输入',
            trigger: 'blur'
          }]
        },{
          label: '12.2、家庭常住人口数',
          key: 'p12_2',
          type: 'input-number',
          unit: '人',
          min: 1,
          max: 50,
          step: 1,
          rules: [{
            required: true,
            message: '请输入',
            trigger: 'blur'
          }]
        }, {
          label: '二、临床检测指标',
          type: 'header',
        },{
          label: '1、体格检查',
          type: 'sub-header',
        },{
          label: '1.1、身高',
          key: 'p13_1',
          type: 'input-number',
          unit: 'cm',
          min: 20,
          max: 280,
          step: 1,
          handleChange: () => {
            if(this.form.p13_1 && this.form.p13_2) {
              this.form.p13_3 = this.form.p13_2 / (this.form.p13_1 / 100) ** 2;
            }
          },
          rules: [{
            required: true,
            message: '请输入',
            trigger: 'blur'
          }]
        },{
          label: '1.2、体重',
          key: 'p13_2',
          type: 'input-number',
          unit: 'kg',
          min: 20,
          max: 280,
          handleChange: () => {
            if(this.form.p13_1 && this.form.p13_2) {
              this.form.p13_3 = this.form.p13_2 / (this.form.p13_1 / 100) ** 2;
            }
          },
          step: 1,
          rules: [{
            required: true,
            message: '请输入',
            trigger: 'blur'
          }]
        },{
          label: '1.3、BMI',
          key: 'p13_3',
          type: 'input-number',
          unit: 'kg/m2',
          disabled: true,
          step: 1,
          rules: [{
            required: true,
            message: '请输入',
            trigger: 'blur'
          }]
        },
        {
          label: '2、肺功能检查',
          key: 'p14',
          type: 'upload',
          handleUploadSuccess: (response, file, fileList) => {
            this.form.p14 = response.data;
          },
          handleRemove: (file, fileList) => {
            this.form.p14 = '';
          },
          rules: []
        },{
          label: '3、FeNO检查',
          key: 'p15',
          type: 'upload',
          handleUploadSuccess: (response, file, fileList) => {
            this.form.p15 = response.data;
          },
          handleRemove: (file, fileList) => {
            this.form.p15 = '';
          },
          rules: []
        },{
          label: '4、血液指标',
          key: 'p16',
          type: 'upload',
          handleUploadSuccess: (response, file, fileList) => {
            this.form.p16 = response.data;
          },
          handleRemove: (file, fileList) => {
            this.form.p16 = '';
          },
          rules: []
        },
      ]
      }
    },
    data() {
        return {
          pcTextArr,
          upload: {
            url: this.context.env.baseUrl + "/admin-api/infra/file/upload",
            headers: {Authorization: "Bearer " + getAccessToken()},
            data: {}
          },
          form: {
              p1: this.context.person.sex === '1' ? '男': '女',
              p2: this.context.person.age,
              p3: [],
              p4: [],
              p5: [],
              p5_2_1: null,
              p5_2_2: null,
              p5_2_3: null,
              p6_1: '',
              p6_1_1: [],
              p6_1_2: null,
              p6_1_3: null,
              p6_1_4: null,
              p7_1: [],
              p7_2: null,
              p7_3: '',
              p7_4: '',
              p8_1: [],
              p8_2: '',
              p8_3: '',
              p9_1: '',
              p9_2: '',
              p9_2_1: '',
              p9_2_2: [],
              p10: [],
              p11: '',
              p12_1: null,
              p12_2: null,
              p13_1: null,
              p13_2: null,
              p13_3: null,
              p14: '',
              p15: '',
              p16: '',
          },
        };
    },
    methods: {
      getData() {
          return this.form;
      },
      validateData() {
          return new Promise((resolve) => {
            this.$refs["form"].validate((valid) => {
                resolve(valid);
            });
          });
      },
      handleUploadSuccess(response, file, fileList) {
        this.form.p6 = response.data;
      },
      handleUploadError(error, file, fileList) {
        this.$modal.msgError("上传失败");
      }
    },
};

export const formConfig = {
  fieldList:[{
    label: '1、性别',
    key: 'p1',
    type: 'radio',
  }, {
    label: '2、年龄',
    key: 'p2',
    type: 'input-number',
  },
  {
    label: '3、籍贯',
    key: 'p3',
    type: 'address-select',
  }, {
    label: '4、生源地',
    key: 'p4',
    type: 'address-select',
  }, {
    label: '5.1、吸烟状态',
    key: 'p5',
    type: 'checkbox',
  },{
    label: '5.2.1、每天吸烟支数',
    key: 'p5_2_1',
    type: 'input-number',
    unit: '支',
  }, {
    label: '5.2.2、吸烟年数',
    key: 'p5_2_2',
    type: 'input-number',
  },{
    label: '5.2.3、吸烟指数',
    key: 'p5_2_3',
    type: 'input-number',
  }, {
    label: '6.1、过去一年饮酒频率',
    key: 'p6_1',
    type: 'radio',
  }, {
    label: '6.1.1、饮酒类型（多选）',
    key: 'p6_1_1',
    type: 'checkbox',
  }, {
    label: '6.1.2、饮酒度数',
    key: 'p6_1_2',
    type: 'input-number',
  },{
    label: '6.1.3、平均每次饮酒量',
    key: 'p6_1_3',
    type: 'input-number',
  },{
    label: '6.1.4、酒精量',
    key: 'p6_1_4',
    type: 'input-number',
  }, {
    label: '7.1、过去一年运动类型（多选）',
    key: 'p7_1',
    type: 'checkbox',
  }, {
    label: '7.2、过去一年运动频率',
    key: 'p7_2',
    type: 'input-number',
  }, {
    label: '7.3、过去一年运动强度',
    key: 'p7_3',
    type: 'radio',
  },{
    label: '7.4、过去一年每次运动持续时间',
    key: 'p7_4',
    type: 'radio',
  }, {
    label: '8.1、饮食偏好（多选）',
    key: 'p8_1',
    type: 'checkbox',
  }, {
    label: '8.2、饮食结构',
    key: 'p8_2',
    type: 'radio',
  }, {
    label: '8.3、新鲜蔬菜水果摄入量',
    key: 'p8_3',
    type: 'radio',
  }, {
    label: '9.1、二手烟暴露情况',
    key: 'p9_1',
    type: 'radio',
  }, {
    label: '9.2、宠物是否饲养宠物',
    key: 'p9_2',
    type: 'radio',
  }, {
    label: '9.2.1、宠物种类',
    key: 'p9_2_1',
    type: 'input-text',
  },{
    label: '9.2.2、饲养宠物时间',
    key: 'p9_2_2',
    type: 'input-date-day',
  }, {
    label: '10、疾病史（多选）',
    key: 'p10',
    type: 'checkbox',
  },{
    label: '11、药物史',
    key: 'p11',
    type: 'input-text',
  }, {
    label: '12.1、家庭可支配收入',
    key: 'p12_1',
    type: 'input-number',
    unit: '元/月',
  },{
    label: '12.2、家庭常住人口数',
    key: 'p12_2',
    type: 'input-number',
    unit: '人',
  }, {
    label: '1.1、身高',
    key: 'p13_1',
    type: 'input-number',
    unit: 'cm',
  },{
    label: '1.2、体重',
    key: 'p13_2',
    type: 'input-number',
    unit: 'kg',
  },{
    label: '1.3、BMI',
    key: 'p13_3',
    type: 'input-number',
    unit: 'kg/m2',
  },
  {
    label: '2、肺功能检查',
    key: 'p14',
    type: 'upload',
  },{
    label: '3、FeNO检查',
    key: 'p15',
    type: 'upload',
  },{
    label: '4、血液指标',
    key: 'p16',
    type: 'upload',
  } ]
}
</script>
<style scoped>
</style>
