<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            size="mini"
        >
            <el-form-item label="1、肿胀关节数（SJC）" prop="p1">
                <el-input
                    @input="handleInput($event, 'p1', 28, 0)"
                    v-model="form.p1"
                    style="width: 250px"
                    ><template slot="append">分</template></el-input
                >
            </el-form-item>

            <el-form-item label="2、压痛关节数（TJC）" prop="p2">
                <el-input
                    @input="handleInput($event, 'p2', 28, 0)"
                    v-model="form.p2"
                    style="width: 250px"
                    ><template slot="append">分</template></el-input
                >
            </el-form-item>
            <el-form-item label="3、患者整体评估（PGA）" prop="p3">
                <el-input
                    @input="handleInput($event, 'p3', 10, 0)"
                    v-model="form.p3"
                    style="width: 250px"
                    ><template slot="append">分</template></el-input
                >
            </el-form-item>
            <el-form-item label="4、医生整体评估（EGA）" prop="p4">
                <el-input
                    @input="handleInput($event, 'p4', 10, 0)"
                    v-model="form.p4"
                    style="width: 250px"
                    ><template slot="append">分</template></el-input
                >
            </el-form-item>
            <el-form-item label="5、类风湿关节炎疾病活动度评分（CDAI）">
                <el-input
                    v-model="form.p5"
                    style="width: 250px"
                    :disabled="true"
                    ><template slot="append">分</template></el-input
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0102",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {},
            // 表单校验
            rules: {
                p1: [
                    {
                        required: true,
                        message: "请输入肿胀关节数（SJC）",
                        trigger: "blur",
                    },
                ],
                p2: [
                    {
                        required: true,
                        message: "请输入压痛关节数（TJC）",
                        trigger: "blur",
                    },
                ],
                p3: [
                    {
                        required: true,
                        message: "请输入患者整体评估（PGA）",
                        trigger: "blur",
                    },
                ],
                p4: [
                    {
                        required: true,
                        message: "请输入医生整体评估（EGA）",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
        handleInput(value, field = 'p1', max = 28, min = 0) {
            this.form[field] = value.toString().replace(/\D/g, "").slice(0, 3);
            if (this.form[field] > max) {
                this.form[field] = max;
            }
            this.handleCount();
        },
        handleCount() {
            let total = 0
            for (var i=1;i<5;i++) {
                total += parseInt(this.form['p' + i] || 0)
            }
            this.form.p5 = total
        },
        handleNumberInput(event, field) {
        if (field == 'p6') {
            let value = event.replace(/[^0-9.]/g, '');

            // 处理多个小数点的情况
            const decimalParts = value.split('.');
            if (decimalParts.length > 2) {
            value = decimalParts[0] + '.' + decimalParts.slice(1).join('');
            }

            // 限制整数部分为3位
            if (decimalParts[0].length > 2) {
            value = decimalParts[0].substring(0, 2) + (decimalParts[1] ? '.' + decimalParts[1] : '');
            }

            // 限制小数部分为2位
            if (decimalParts.length > 1 && decimalParts[1].length > 2) {
            value = decimalParts[0] + '.' + decimalParts[1].substring(0, 2);
            }
            this.form.p5 = value;
        } else {
            // 移除非数字字符
            let num = event.replace(/\D/g, '')
            
            // 限制长度为3位
            if (num.length > 3) {
            num = num.slice(0, 3)
            }
            // 更新绑定值
            this.form[field] = num
            this.calYTB()
        }


        },
    },
};
</script>
