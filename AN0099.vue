<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            label-position="top"
            size="mini"
        >
            <el-form-item label="请指出你的鼻部症状" prop="p1">
                <Slider :value="form.p1" @input="(val) => form.p1 = val" :min="1" :max="10" :step="1" leftText="无症状" rightText="症状最重" />
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import Slider from './components/Slider.vue';
export default {
    name: "AN0099",
    props: {
        formData: String,
    },
    components: {
        Slider
    },
    created() {
        if (this.formData) {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {
                p1: 1,
            },
        };
    },
    watch: {
        'form.p1': {
            handler(newVal) {
               console.log(newVal);
            },
        },
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            return new Promise((resolve) => {
                this.$refs["form"].validate((valid) => {
                    resolve(valid);
                });
          });
        },
    },
};

export const formConfig = {
  fieldList: [{
    label: '请指出你的鼻部症状',
    key: 'p1',
  }]
}
</script>
<style scoped>
.process {
    width: 100%;
}
.process-title {
    display: flex;
    justify-content: space-between;
}

.process-box {
    display: flex;
    border: 1px solid #d7d7d7;
}
.process-box div {
    width: 10%;
    text-align: center;
    padding: 4px;
    cursor: pointer;
}
.click-box {
    background-color: #d7d7d7;
}
/deep/.el-form-item__label{
    font-weight: 450;
    font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
    color: #000000;
}
</style>
