<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            label-position="top"
            size="mini"
        >
            <template v-for="item in formList">
                <el-form-item :key="item.key" :prop="item.key" :label="item.label" :rules="item.rules" v-if="!item?.isHidden">
                    <el-radio-group v-if="item.type === 'radio'" v-model="form[item.key]" @change="item.handleChange&&item.handleChange()">
                        <el-radio v-for="label in item.options" :key="label" :label="label" :value="label" class="!m-2" border></el-radio>
                    </el-radio-group>
                    <el-checkbox-group v-if="item.type === 'checkbox'" v-model="form[item.key]" @change="item.handleChange&&item.handleChange()">
                      <el-checkbox v-for="label in item.options" :key="label" :label="label" :value="label" class="!m-2" border></el-checkbox>
                    </el-checkbox-group>
                    <el-input v-model="form[item.key]" class="!m-2" type="number" v-if="item.type === 'input'" placeholder="请输入"></el-input>
                    <Slider :value="form[item.key]" @input="(val) => form[item.key] = val" v-if="item.type === 'slider'" :min="0" :max="10" :step="1" >
                        <template #left>
                            <div class="flex flex-col gap-1 items-center py-2">
                                <span class="text-gray-500 text-xs">无症状</span>
                                <img src="./assets/images/good.png" alt="slider-left" class="size-12 "></img>
                            </div>
                        </template>
                        <template #right>
                            <div class="flex flex-col gap-1 items-center py-2">
                                <span class="text-gray-500 text-xs">症状最严重</span>
                                <img src="./assets/images/bad.png" alt="slider-right" class="size-12 "></img>
                            </div>
                        </template>
                    </Slider>
                </el-form-item>
            </template>
        </el-form>
    </div>
</template>

<script>
import Slider from './components/Slider.vue';
export default {
    name: "AN0108",
    components: {
      Slider
    },
    props: {
        formData: String,
        context: {
          type: Object,
          default: () => ({
              person: {}
          })
        }
    },
    mounted() {
       if (this.formData) {
            this.form = JSON.parse(this.formData);
        }
    },
    computed: {
      formList() {
        return [{
          label: '1、您是否曾在直接接触宠物、到访宠物饲养的地点或接触宠物饲养者后出现以下症状（多选)',
          key: 'p1',
          type: 'checkbox',
          options: ['打喷嚏、流鼻涕、鼻塞、鼻痒', '呼吸短促、胸闷、喘息、呼吸困难、咳嗽或喉咙发痒', '皮疹伴瘙痒', '眼睛发红、流泪、瘙痒或异物感', '严重的过敏反应或过敏性休克', '不存在以上症状'],
          handleChange: () => {
            if(this.form.p1.includes('不存在以上症状')) {
              this.form.p1 = ['不存在以上症状'];
              this.form.p2 = [];
              this.form.p3 = [];
              this.form.p4 = '';
              this.form.p5 = '';
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.1、上述宠物过敏症状在接触何种动物后出现？（多选）',
          key: 'p2',
          type: 'checkbox',
          isHidden: this.form.p1.includes('不存在以上症状'),
          options: ['猫', '狗', '兔子', '鼠', '鸟类', '其他'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.2、上述宠物过敏症状于何种情况下出现？（多选）',
          key: 'p3',
          type: 'checkbox',
          isHidden: this.form.p1.includes('不存在以上症状'),
          options: ['直接接触宠物皮毛、皮屑、分泌物、排泄物后', '到访宠物饲养的地点后', '接触宠物饲养者后'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.3、您从几岁时出现上述宠物过敏症状？',
          key: 'p4',
          type: 'input',
          isHidden: this.form.p1.includes('不存在以上症状'),
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        },  {
          label: '1.4、您自我感觉因宠物过敏症状所带来的不适感严重程度如何？（最左端为0，表示您不受过敏症状的困扰；最右端为10，表示过敏症状极其困扰您）',
          key: 'p5',
          type: 'slider',
          isHidden: this.form.p1.includes('不存在以上症状'),
        }]
      }
    },
    data() {
        return {
          form: {
            p1: [],
            p2: [],
            p3: [],
            p4: '',
            p5: '',
          },
        };
    },
    methods: {
      getData() {
          return this.form;
      },
      validateData() {
          return new Promise((resolve) => {
            this.$refs["form"].validate((valid) => {
                resolve(valid);
            });
          });
      },
    },
};

export const formConfig = {
  fieldList: [{
    label: '1、您是否曾在直接接触宠物、到访宠物饲养的地点或接触宠物饲养者后出现以下症状（多选)',
    key: 'p1',
    type: 'checkbox',
  }, {
    label: '1.1、上述宠物过敏症状在接触何种动物后出现？（多选）',
    key: 'p2',
    type: 'checkbox',
  }, {
    label: '1.2、上述宠物过敏症状于何种情况下出现？（多选）',
    key: 'p3',
    type: 'checkbox',
  }, {
    label: '1.3、您从几岁时出现上述宠物过敏症状？',
    key: 'p4',
    type: 'input',
  },  {
    label: '1.4、您自我感觉因宠物过敏症状所带来的不适感严重程度如何？（最左端为0，表示您不受过敏症状的困扰；最右端为10，表示过敏症状极其困扰您）',
    key: 'p5',
    type: 'slider',
  }]
}
</script>
<style scoped>
</style>
