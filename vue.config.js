const path = require('path')
const { defineConfig } = require('@vue/cli-service');
const { version } = require('./package.json');

function resolve(dir) {
  return path.join(__dirname, dir)
}

process.env.VUE_APP_VERSION = version;

const name = process.env.VUE_APP_TITLE || '医学人工智能操作系统' // 网页标题
const port = process.env.port || process.env.npm_config_port || 80 // 端口

module.exports = defineConfig({
  lintOnSave: true,
  productionSourceMap: true,
  devServer: {
    port: port,
    allowedHosts: 'all',
    compress: true,
    proxy: {
      '/proxy-api': {
        target: 'http://***********:48081',
        changeOrigin: true,
        pathRewrite: { '^/proxy-api': '' }
      },
      '/dify-api': {
        target: 'http://***************',
        changeOrigin: true,
        pathRewrite: { '^/dify-api': '' }
      }
    }
  },
  configureWebpack: {
    name: name,
    entry: './src/main.ts',
    devtool: 'source-map',
    resolve: {
      extensions: ['.ts', '.js', '.vue', '.json', '.scss', '.css', '.svg', '.tsx'],
      alias: {
        '@': resolve('src'),
        '@action-form': resolve('action-form'),
      },
    },
    module: {
      rules: [
        {
          test: /\.tsx?$/,
          loader: 'ts-loader',
          options: {
            appendTsSuffixTo: [/\.vue$/],
            transpileOnly: true
          }
        },
      ],
    },
    plugins: [
      // new DeadCodePlugin({
      //   patterns: ['src/**/*'],
      //   exclude: ['src/assets/icons/**/*', 'src/assets/images/**/*', 'src/assets/fonts/**/*', 'src/assets/videos/**/*', 'src/assets/audio/**/*', 'src/assets/docs/**/*', 'src/assets/other/**/*']
      // }),
      // new HtmlWebpackPlugin({
      //   template: './public/index.html', // 你的 HTML 模板
      //   filename: 'index.html',
      //   inject: true, // 自动注入脚本
      // }),
    ]
  },
  chainWebpack: (config) => {
    config.module.rule('svg').exclude.add(resolve('src/assets/icons/svg')).end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
  },
  css: {
    loaderOptions: {
      postcss: {
        postcssOptions: {
          plugins: [
            // 你可以在这里添加 PostCSS 插件，例如 autoprefixer
            require('autoprefixer')
          ]
        }
      }
    }
  }
})
