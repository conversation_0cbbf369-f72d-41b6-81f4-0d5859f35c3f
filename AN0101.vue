<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            label-position="top"
            size="mini"
        >
            <el-form-item v-for="item in formList" :key="item.key" :prop="item.key" :label="item.label" :rules="item.rules">
                <el-checkbox-group v-model="form[item.key]" @change="handleChange(item.key)">
                    <el-checkbox v-for="label in item.options" :key="label" :label="label" :value="label" class="!m-2" border></el-checkbox>
                </el-checkbox-group>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
const formList = [{
  label: '1、您是否被确诊患有以下疾病？（多选）',
  key: 'p1',
  options: ['支气管哮喘', '过敏性鼻炎', '特应性皮炎/湿疹', '花粉症', '食物过敏', '过敏性结膜炎', '宠物过敏', '药物过敏', '无上述疾病'],
  rules: [{
    required: true,
    message: '请选择',
    trigger: 'blur'
  }]
}, {
  label: '2、您是否被确诊以下合并症？（多选）',
  key: 'p2',
  options: ['慢性鼻窦炎', '鼻息肉', '分泌性中耳炎', '嗅觉障碍', '腺样体肥大', '上气道咳嗽综合征', '阻塞性睡眠呼吸暂停低通气综合征', '慢性阻塞性肺疾病', '失眠', '注意缺陷多动障碍', '头痛', '嗜酸性粒细胞性食管炎', '过敏性紫癜', '无上述合并症'],
  rules: [{
    required: true,
    message: '请选择',
    trigger: 'blur'
  }]
} ]
export default {
    name: "AN0101",
    props: {
        formData: String,
        context: {
          type: Object,
          default: () => ({
              person: {}
          })
        }
    },
    mounted() {
       if (this.formData) {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            formList,
            form: {
              p1: [],
              p2: [],
            },
        };
    },
    methods: {
      getData() {
          return this.form;
      },
      validateData() {
          return new Promise((resolve) => {
            this.$refs["form"].validate((valid) => {
                resolve(valid);
            });
          });
      },
      handleChange(key) {
        if(this.form[key].includes('无上述疾病')) {
          this.form[key] = ['无上述疾病']
        }
        if(this.form[key].includes('无上述合并症')) {
          this.form[key] = ['无上述合并症']
        }
      }
    },
};

export const formConfig = {
  fieldList: [{
    label: '1、您是否被确诊患有以下疾病？（多选）',
    key: 'p1',
  }, {
    label: '2、您是否被确诊以下合并症？（多选）',
    key: 'p2',
  } ]
}
</script>
<style scoped>
</style>
