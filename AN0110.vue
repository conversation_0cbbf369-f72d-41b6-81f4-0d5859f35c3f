<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            label-position="top"
            size="mini"
        >
          <h4 class="text-xl leading-8 text-center mb-4">基于人群的哮喘及过敏性疾病筛查和管理研究</h4>
            <div class="text-md leading-8 flex flex-col gap-2">
                <p class="font-bold">01 我们的初衷</p>
                <p>哮喘及过敏性疾病在我国患病率显著上升，严重影响患者生活质量，已成为日益严重的重大公共卫生问题。然而，当前哮喘及过敏性疾病的诊断率仍偏低，公众对哮喘及过敏性疾病的认识尚存不足。为此，我们启动本项目，旨在帮助您更全面的了解哮喘及过敏相关健康知识，更早识别哮喘及过敏性疾病患病风险，，有效地监测健康状态，进而提升生活质量。</p>
                <p class="font-bold">02 我们将要做的事</p>
                <p>1. 哮喘及过敏性疾病风险筛查：我们将使用符合我国人群特征的标准化筛查问卷，帮助您评估哮喘及过敏性疾病患病风险，并提供个体化检查，帮助您了解哮喘及过敏性疾病的相关健康状况。</p>
                <p>2. 提供个性化健康监测与指导：根据哮喘及过敏性疾病风险筛查结果，我们会解读您患哮喘和过敏性疾病的风险等级，为您提供个体化健康监测。</p>
                <p>3. 建立个人健康档案：我们将整合您的健康筛查数据，为您建立专属健康档案，持续动态监测您的健康状态变化，便于您全面地掌握哮喘和过敏性疾病发病风险及发展趋势，有助于进行精细化健康管理。</p>
                <p class="font-bold">03 我们的团队</p>
                <p>北京医院呼吸与危重症医学科作是全国呼吸临床重点专科，主持制定呼吸领域主要的行业规范和国家标准，牵头承担多项国家重点研究项目。在哮喘的流行病学调查、危险因素研究、非药物干预领域积累了丰富的研究经验。</p>
                <p>中国医学科学院/北京协和医学院群医学及公共卫生学院长期致力于公共卫生、流行病学、慢性病管理及健康干预等领域的创新研究，在多项大型研究中积累了大量现场调查和人群研究经验，为我国的公共卫生政策制定提供了重要的支持。</p>
                <p>吉林大学公共卫生学院长期从事慢性病及人群健康影响因素相关的流调工作，承担了多项国家科技攻关、自然科学基金及省部级项目，在人群膳食相关疾病和流行病学调查中具有丰富的研究经验。</p>
                <p>北京壹生慈善基金会主要致力于扶贫济困、助老扶残、助医助学、培养公共卫生人才、支持医疗卫生事业的发展，在应对自然灾害，公共卫生事件等方面开展各类公益活动，在2022年度社会组织等级评估中获得3A级。</p>
            </div>
        </el-form>
    </div>
</template>

<script>
import _ from 'lodash'
export default {
    name: "AN0110",
    props: {
        formData: String,
        context: {
          type: Object,
          default: () => ({
              person: {}
          })
        }
    },
    mounted() {
       if (this.formData) {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
          form: {
            p1: '1',
          },
        };
    },
    methods: {
      getData() {
          return {
            p1: '1',
          };
      },
      validateData() {
          return true;
      },
    },
};
</script>
<style scoped>
</style>
