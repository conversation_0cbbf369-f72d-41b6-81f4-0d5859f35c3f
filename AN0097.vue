<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            size="mini"
        >
            <el-form-item prop="p1" label="1、您是否存在以下病史（多选）">
                <el-checkbox-group v-model="form.p1" @change="handleChange">
                    <el-checkbox v-for="label in options" :key="label" :label="label" :value="label" border></el-checkbox>
                    <el-checkbox label="不存在以上情况" value="不存在以上情况" border></el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item v-show="form.p1.length" label="2、患病时间（年）"></el-form-item>
            <el-form-item v-for="(label, index) in options" v-show="checkShow(label)" :rules="getRules(index)" :key="label" :prop="`p${index + 2}`">
                <div class="input-box">
                    <div class="input-title">{{ label }}</div>
                    <el-input-number
                        v-model="form['p' + (index + 2)]"
                        style="width: 150px"
                        :precision="0"
                        :max="100"
                        :min="1"
                    ></el-input-number>
                </div>
                <div class="process">
                    <div class="process-box">
                        <div
                            v-for="(item, index2) in 20"
                            :class="getClass('p' + (index + 2), item)"
                            :key="`${index}-${index2}`"
                            @click="clickNode('p' + (index + 2), item)"
                        >
                            {{ item }}
                        </div>
                    </div>
                </div>
            </el-form-item>
            <!-- <el-form-item v-show="isShowP3" :label="isShowP2 ? '' : '2、患病时间（年）'" prop="p3">
                <div class="input-box">
                    <div class="input-title">高血压</div>
                    <el-input-number
                        v-model="form.p3"
                        style="width: 150px"
                        :precision="0"
                        :max="100"
                        :min="1"
                    ></el-input-number>
                </div>
                <div class="process">
                    <div class="process-box">
                        <div
                            v-for="(item, index) in 20"
                            :class="getClass('p3', item)"
                            :key="index"
                            @click="clickNode('p3',item)"
                        >
                            {{ item }}
                        </div>
                    </div>
                </div>
            </el-form-item> -->
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0034",
    props: {
        formData: String,
        context: {
        type: Object,
        default: () => ({
            person: {}
        })
        }
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
        if (this.context?.person != null && this.context?.person != '') {
            let sexs = ['女', '男']
            this.form.age = this.context.person.age;
            this.form.sex = sexs[this.context.person.sex] || '未知';
        }
    },
    computed: {
        getClass() {
            return (field, item) => {
                if (this.form[field]) {
                    return {'click-box': this.form[field] >= item, 'overflow-box': this.form[field] > 20}
                }

            };
        },
        checkShow() {
            return (label) => {
                if (typeof  this.form.p1 == 'string') {
                    return this.form.p1 == label
                }
                return this.form.p1.includes(label)
            }
        }
    },
    data() {
        return {
            form: {
                p1: [],
            },
            // 病症种类
            options: ["急性冠状动脉综合征", "稳定性冠状动脉粥样硬化性心脏病", "血运重建术后", "缺血性心肌病", "外周动脉粥样硬化性疾病", "高血压", "糖尿病", "冠心病", "房颤", "脑卒中", "心衰", "周围血管病", "类风湿关节炎", "系统性红斑狼疮", "肝肾功能不全等病史"],
            // 表单校验
            rules: {
                // p1:绑定的名字
                p1: [
                    {
                        required: true,
                        message: "请选择疼痛状态",
                        trigger: "blur",
                    },
                ],
                // p2: [
                //     {
                //         required: true,
                //         message: "请填写急性冠状动脉综合征",
                //         trigger: "blur",
                //     },
                // ],
                // p3: [
                //     {
                //         required: true,
                //         message: "请填写高血压",
                //         trigger: "blur",
                //     },
                // ],
            },
        };
    },
    methods: {
        getRules(index) {
            return [
                {
                    required: true,
                    message: `请填写${this.options[index]}`,
                    trigger: "blur",
                    validator: (rule, value, callback) => {
                        if (this.form.p1 == this.options[index] || this.form.p1.includes(this.options[index])) {
                            if (value == null || value == undefined || value == '') {
                                callback(new Error(`请填写${this.options[index]}`));
                            } else {
                                callback();
                            }
                        } else {
                            callback();
                        }
                    }
                },
            ]
        },
        handleChange(e) {

            if (e.length > 1 && e[0] == '不存在以上情况') {
                this.form.p1 = e.splice(1)
            } else if (e.length > 1 && e.indexOf('不存在以上情况') != -1) {
                this.form.p1 = ['不存在以上情况']
            }

            this.options.forEach((label, index) => {
                if (this.form.p1.indexOf(label) == -1) {
                    this.form[`p${index + 2}`] = undefined
                }
            })
        },
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
        clickNode(field, index) {
            this.$set(this.form, field, index);
            this.$refs['form'].validateField(field);
        },
    },
};
</script>
<style scoped>
.process {
    /* width: 80%; */
}
.process-title {
    display: flex;
    justify-content: space-between;
}

.process-box {
    display: flex;
    border: 1px solid #d7d7d7;
}
.process-box div {
    width: 10%;
    text-align: center;
    padding: 4px;
    cursor: pointer;
    flex: 1;
}
.click-box {
    /* background-color: #d7d7d7; */
    background-color: #bce0f0;
}
.overflow-box {
    background-color: #d7d7d7;
}

.input-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
}
.input-title {
    font-size: 13px;
    margin-right: 10px;
}
.input-title::before {
    content: "*";
    color: #F56C6C;
    font-weight: 450;
}

/deep/.el-checkbox--mini.is-bordered {
    padding: 6px 15px 0 15px !important;
    border-radius: 3px;
    height: 28px;
  }

  /deep/.el-checkbox__input {
    display: none !important;
    white-space: nowrap;
    cursor: pointer;
    outline: 0;
    line-height: 1;
    vertical-align: middle;
  }

  /deep/.el-checkbox__label {
    padding-left: 0px;
  }

  /deep/.el-form-item__label {
    text-align: right;
    vertical-align: middle;
    /* float: left; */
    color: #606266;
    line-height: 40px;
    padding: 0 12px 0 0;
    box-sizing: border-box;
    font-weight: 450;
    font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
    color: #000000;
  }

  /deep/.el-checkbox-group {
    font-size: 0;
    margin-top: 7px;
  }

  /deep/.el-checkbox {
    color: #000000;
    font-weight: 400;
    line-height: 1;
    cursor: pointer;
    white-space: nowrap;
    outline: 0;
    margin-right: 10px;
    margin-bottom: 10px;
  }
</style>
