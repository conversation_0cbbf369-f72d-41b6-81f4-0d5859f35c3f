<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            label-position="top"
            size="mini"
        >
            <template v-for="item in formList">
                <el-form-item :key="item.key" :prop="item.key" :label="item.label" :rules="item.rules" v-if="!item?.isHidden">
                    <el-radio-group v-if="item.type === 'radio'" v-model="form[item.key]" @change="item.handleChange&&item.handleChange()">
                        <el-radio v-for="label in item.options" :key="label" :label="label" :value="label" class="!m-2" border></el-radio>
                    </el-radio-group>
                    <el-checkbox-group v-if="item.type === 'checkbox'" v-model="form[item.key]" @change="item.handleChange&&item.handleChange()">
                      <el-checkbox v-for="label in item.options" :key="label" :label="label" :value="label" class="!m-2" border></el-checkbox>
                    </el-checkbox-group>
                    <el-input v-model="form[`${item.key}_input`]" class="!m-2" v-if="item.showInput" placeholder="请输入"></el-input>
                    <el-input v-model="form[item.key]" class="!m-2" v-if="item.type === 'input'" type="number" min="1" max="200" placeholder="请输入">
                      <template #append>岁</template>
                    </el-input>
                </el-form-item>
            </template>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0107",
    props: {
        formData: String,
        context: {
          type: Object,
          default: () => ({
              person: {}
          })
        }
    },
    mounted() {
       if (this.formData) {
            this.form = JSON.parse(this.formData);
        }
    },
    computed: {
      formList() {
        return  [{
          label: '1、食用或接触某种食物后，您是否曾出现不适症状?',
          key: 'p1',
          type: 'radio',
          options: ['是', '否'],
          handleChange: () => {
            if(this.form.p1 === '否') {
              this.form.p2 = [];
              this.form.p3 = [];
              this.form.p4 = null;
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.1、食用或接触某种食物后是否出现过以下不适症状?（多选）',
          key: 'p2',
          type: 'checkbox',
          isHidden: this.form.p1 !== '是',
          options: ['唇或口腔刺痛', '麻木', '嘴唇、口腔、上颚或耳朵剧烈瘙痒', '嘴唇、舌头、口腔或喉咙肿胀', '喉咙发痒/疼痛，或喉咙有异物感', '流涕、流泪、鼻充血、打喷嚏', '眶周肿胀瘙痒', '哮喘', '荨麻疹（皮肤风团、红斑）', '皮肤水肿，或湿疹', '恶心、呕吐、腹痛、腹泻 或腹胀', '严重过敏反应（迅速出现皮肤发红，伴有严重呼吸困难或晕厥）', '其他'],
          showInput: this.form.p2.includes('其他'),
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.2、您在食用或接触何种食物后会出现上述不适反应？（多选）',
          key: 'p3',
          type: 'checkbox',
          isHidden: this.form.p1 !== '是',
          options: ['水果或蔬菜', '豆芽、沙拉叶或香草', '坚果，包括所有树坚果和花生', '削皮的土豆或其他根茎类蔬菜', '扁豆、鹰嘴豆或其他豆类', '牛奶、鸡蛋、鸡肉', '鱼、贝类等海鲜', '含麸质食物（小麦、黑麦、大麦、燕麦及其制品）', '红酒、啤酒、白酒等含酒精类产品', '其他'],
          showInput: this.form.p3.includes('其他'),
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.2.1、如果您在食用或接触A栏的任一食物后出现症状，那么您的症状反应会在以下何种情况下发生',
          key: 'p4',
          type: 'radio',
          isHidden: this.form.p1 !== '是',
          options: ['只有生食时出现', '生食及熟食都会出现', '只有熟食时出现', '不确定'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        },  {
          label: '1.3、您在食用或接触食物多久后会出现不适反应？',
          key: 'p5',
          type: 'radio',
          isHidden: this.form.p1 !== '是',
          options: ['接触嘴唇时', '咬或咀嚼时', '吞咽后5分钟内', '进食后15分钟内', '进食后30分钟内', '进食后1-2小时', '进食后2-4小时', '进食后6小时以上'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.4、如果您曾在食用或接触食物后出现中任一症状，那么您是从几岁开始出现此反应的？',
          key: 'p6',
          type: 'input',
          isHidden: this.form.p1 !== '是',
          rules: [{
            validator: (rule, value, callback) => {
              if(value < 1 || value > 200) {
                callback(new Error('请输入0-200之间的数字'));
              }
              callback();
            },
          }]
        }]
      }
    },
    data() {
        return {
          form: {
            p1: null,
            p2: [],
            p2_input: '',
            p3: [],
            p3_input: '',
            p4: null,
            p5: null,
            p6: null,
          },
        };
    },
    methods: {
      getData() {
          return this.form;
      },
      validateData() {
          return new Promise((resolve) => {
            this.$refs["form"].validate((valid) => {
                resolve(valid);
            });
          });
      },
    },
};

export const formConfig = {
  fieldList: [{
    label: '1、食用或接触某种食物后，您是否曾出现不适症状?',
    key: 'p1',
    type: 'radio',
  }, {
    label: '1.1、食用或接触某种食物后是否出现过以下不适症状?（多选）',
    key: 'p2',
    type: 'checkbox',
  }, {
    label: '1.2、您在食用或接触何种食物后会出现上述不适反应？（多选）',
    key: 'p3',
    type: 'checkbox',
  }, {
    label: '1.2.1、如果您在食用或接触A栏的任一食物后出现症状，那么您的症状反应会在以下何种情况下发生',
    key: 'p4',
    type: 'radio',
  },  {
    label: '1.3、您在食用或接触食物多久后会出现不适反应？',
    key: 'p5',
    type: 'radio',
  }, {
    label: '1.4、如果您曾在食用或接触食物后出现中任一症状，那么您是从几岁开始出现此反应的？',
    key: 'p6',
    type: 'input',
  }]
}
</script>
<style scoped>
</style>
