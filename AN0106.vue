<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            label-position="top"
            size="mini"
        >
            <template v-for="item in formList">
                <el-form-item :key="item.key" :prop="item.key" :label="item.label" :rules="item.rules" v-if="!item?.isHidden">
                    <el-radio-group v-if="item.type === 'radio'" v-model="form[item.key]" @change="item.handleChange&&item.handleChange()">
                        <el-radio v-for="label in item.options" :key="label" :label="label" :value="label" class="!m-2" border></el-radio>
                    </el-radio-group>
                    <el-checkbox-group v-if="item.type === 'checkbox'" v-model="form[item.key]">
                        <el-checkbox v-for="label in item.options" :key="label" :label="label" :value="label" class="!m-2" border></el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
            </template>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0106",
    props: {
        formData: String,
        context: {
          type: Object,
          default: () => ({
              person: {}
          })
        }
    },
    mounted() {
       if (this.formData) {
            this.form = JSON.parse(this.formData);
        }
    },
    computed: {
      formList() {
        return [{
          label: '1、您是否曾经患有持续至少6个月、反复出现的瘙痒性皮疹?',
          key: 'p1',
          type: 'radio',
          options: ['是', '否'],
          handleChange: () => {
            if(this.form.p1 === '否') {
              this.form.p2 = null;
              this.form.p3 = null;
              this.form.p4 = null;
              this.form.p5 = [];
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.1、在过去12个月内，您是否曾经出现过上述瘙痒性皮疹?',
          key: 'p2',
          type: 'radio',
          isHidden: this.form.p1 !== '是',
          options: ['是', '否'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.2、该瘙痒性皮疹是否曾出现在以下任一部位：肘窝、膝窝、踝前部、臀部下方，或颈部、耳周、眼周?',
          key: 'p3',
          type: 'radio',
          isHidden: this.form.p1 !== '是',
          options: ['是', '否'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.3、在过去12个月内，该瘙痒性皮疹是否曾完全消退过?（完全消退是指皮肤上原本出现的各种皮损完全消失，皮肤恢复到正常的外观和质地，且不再有与皮疹相关的任何症状，如瘙痒、疼痛、脱屑等）',
          key: 'p4',
          type: 'radio',
          isHidden: this.form.p1 !== '是',
          options: ['是', '否'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.4、在过去12个月内，平均每周因该瘙痒性皮疹夜间被惊醒的次数为',
          key: 'p5',
          type: 'radio',
          isHidden: this.form.p1 !== '是',
          options: ['在过去12个月内从未', '每周少于一晚', '每周一晚或以上'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        } ];
      }
    },
    data() {
        return {
          form: {
            p1: null,
            p2: null,
            p3: null,
            p4: null,
            p5: [],
          },
        };
    },
    methods: {
      getData() {
          return this.form;
      },
      validateData() {
          return new Promise((resolve) => {
              this.$refs["form"].validate((valid) => {
                  resolve(valid);
              });
          });
      },
    },
};

export const formConfig = {
  fieldList: [{
    label: '1、您是否曾经患有持续至少6个月、反复出现的瘙痒性皮疹?',
    key: 'p1',
    type: 'radio',
  }, {
    label: '1.1、在过去12个月内，您是否曾经出现过上述瘙痒性皮疹?',
    key: 'p2',
    type: 'radio',
  }, {
    label: '1.2、该瘙痒性皮疹是否曾出现在以下任一部位：肘窝、膝窝、踝前部、臀部下方，或颈部、耳周、眼周?',
    key: 'p3',
    type: 'radio',
  }, {
    label: '1.3、在过去12个月内，该瘙痒性皮疹是否曾完全消退过?（完全消退是指皮肤上原本出现的各种皮损完全消失，皮肤恢复到正常的外观和质地，且不再有与皮疹相关的任何症状，如瘙痒、疼痛、脱屑等）',
    key: 'p4',
    type: 'radio',
  }, {
    label: '1.4、在过去12个月内，平均每周因该瘙痒性皮疹夜间被惊醒的次数为',
    key: 'p5',
    type: 'radio',
  } ]
}
</script>
<style scoped>
</style>
