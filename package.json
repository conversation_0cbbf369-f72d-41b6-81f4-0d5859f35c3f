{"name": "iris-ui-admin", "version": "0.8.0", "description": "医学人工智能操作系统", "author": "XXX", "license": "MIT", "scripts": {"local": "vue-cli-service serve --mode local", "start": "vue-cli-service serve", "front": "vue-cli-service serve --mode front", "init:submodule": "rm -rf action-form && git clone ********************************************************/project/action-form.git", "build:prod": "npx vue-cli-service build --mode prod", "build:stage": "npx vue-cli-service build --mode stage", "build:dev": "npx vue-cli-service build --mode dev", "build:static": "npx vue-cli-service build --mode static", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "clean": "rimraf node_modules"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://github.com/YunaiV/ruoyi-vue-pro"}, "dependencies": {"@babel/parser": "7.18.4", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^2.0.10", "@microsoft/fetch-event-source": "^2.0.1", "@riophae/vue-treeselect": "0.4.0", "@vueuse/core": "^11.3.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "0.27.2", "benz-amr-recorder": "^1.1.5", "bpmn-js-token-simulation": "0.10.0", "clipboard": "2.0.8", "copy-to-clipboard": "^3.3.3", "core-js": "^3.41.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "5.4.0", "element-china-area-data": "^6.1.0", "element-ui": "2.15.14", "file-saver": "2.0.5", "fuse.js": "6.6.2", "highlight.js": "9.18.5", "inversify": "^7.6.1", "js-beautify": "1.13.0", "jsencrypt": "^3.3.1", "markdown-it": "^14.1.0", "min-dash": "3.5.2", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "qrcode.vue": "^1.7.0", "quill": "1.3.7", "reflect-metadata": "^0.2.2", "screenfull": "5.0.2", "sortablejs": "1.10.2", "sweetalert2": "^11.22.0", "three": "^0.178.0", "throttle-debounce": "2.1.0", "vue": "^2.7.16", "vue-count-to": "1.0.13", "vue-cropper": "0.5.8", "vue-meta": "^2.4.0", "vue-quill-editor": "^3.0.6", "vue-router": "^3.6.5", "vue-video-player": "^5.0.2", "vuedraggable": "2.24.3", "vuex": "3.6.2", "webpack-deadcode-plugin": "^0.1.17", "xml-js": "1.6.11"}, "devDependencies": {"@types/lodash": "^4.17.19", "@types/node": "^24.0.4", "@types/nprogress": "^0.2.3", "@types/three": "^0.178.1", "@vue/cli-plugin-babel": "~5.0.6", "@vue/cli-plugin-eslint": "~5.0.6", "@vue/cli-service": "~5.0.6", "@vue/compiler-sfc": "^3.5.13", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "bpmn-js": "^8.9.0", "bpmn-js-properties-panel": "^0.46.0", "chalk": "^5.0.0", "compression-webpack-plugin": "^9.0.0", "connect": "^3.7.0", "css-loader": "^7.1.2", "eslint": "^8.0.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-vue": "^9.15.0", "fs-extra": "^10.0.0", "lint-staged": "^12.5.0", "postcss-loader": "^8.1.1", "runjs": "^4.4.2", "sass": "^1.49.0", "sass-loader": "^12.0.0", "style-loader": "^4.0.0", "svg-sprite-loader": "^6.0.11", "terser-webpack-plugin": "^5.0.0", "ts-loader": "^8.4.0", "typescript": "^5.8.3", "vue-loader": "^17.4.2", "vue-svg-loader": "^0.16.0", "webpack": "^5.99.9", "webpack-bundle-analyzer": "^4.0.0"}, "engines": {"node": ">= 10.13.0", "npm": ">= 5.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}