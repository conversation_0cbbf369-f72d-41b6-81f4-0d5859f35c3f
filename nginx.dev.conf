user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;
    sendfile        on;
    keepalive_timeout  65;

   server {
        listen       8081;
        listen  [::]:8081;
        server_name  localhost;
        client_max_body_size 20M;

        location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }
        location /api/ {
            proxy_pass http://**********:48081/;
        }
        location /dify-api/ {
            proxy_pass http://***************/;
        }
        error_page   500 502 503 504 404  /err.html;
        location = /err.html {
            root   /usr/share/nginx/html;
        }
    }
}
