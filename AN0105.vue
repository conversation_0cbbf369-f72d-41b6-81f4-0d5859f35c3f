<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            label-position="top"
            size="mini"
        >
            <template  v-for="item in formList">
                <el-form-item :key="item.key" :prop="item.key" :label="item.label" :rules="item.rules" v-if="!item?.isHidden">
                    <el-radio-group v-model="form[item.key]" @change="item.handleChange&&item.handleChange()">
                        <el-radio v-for="label in item.options" :key="label" :label="label" :value="label" class="!m-2" border></el-radio>
                    </el-radio-group>
                </el-form-item>
            </template>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0105",
    props: {
        formData: String,
        context: {
          type: Object,
          default: () => ({
              person: {}
          })
        }
    },
    mounted() {
       if (this.formData) {
            this.form = JSON.parse(this.formData);
        }
    },
    computed: {
      formList() {
        return [{
          label: '1、在过去的任何时间里，您是否出现过喘息（呼吸过程中气道发出的持续、粗糙的声音）或哮鸣音（呼吸时伴随“咻咻”哨音）?',
          key: 'p1',
          options: ['是', '否'],
          handleChange: () => {
            console.log(this.form.p1);
            if(this.form.p1 === '否') {
              this.form.p2 = null;
              this.form.p3 = null;
              this.form.p4 = null;
            }
          },
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.1、在过去的12个月里，您是否出现过喘息或哮鸣音？',
          key: 'p2',
          isHidden: this.form.p1 !== '是',
          options: ['是', '否'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.2、在过去的12个月里，您发生过多少次喘息发作？（呼吸症状突然明显变差，喘息、胸闷、咳嗽明显加重，呼吸困难，以至于需要比平时用更多药物或者去看医生才能控制住即为一次喘息发作）',
          key: 'p3',
          isHidden: this.form.p1 !== '是',
          options: ['是', '否'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '1.3、在过去的12个月里，您平均多久出现因喘息而夜间憋醒?',
          key: 'p4',
          isHidden: this.form.p1 !== '是',
          options: ['没有过', '1～3次', '4～12次', '超过12次'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '2、在过去的12个月里，您是否在运动中或运动后出现咳嗽、喘息或者哮鸣音?',
          key: 'p5',
          options: ['从未因喘息而憋醒', '每周少于一晚', '每周一个或多个晚上', '每晚都因喘息而憋醒'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        }, {
          label: '3、在过去的12个月里，除了因感冒或胸部感染引起的咳嗽外，您是否在夜间出现过干咳？',
          key: 'p6',
          options: ['是', '否'],
          rules: [{
            required: true,
            message: '请选择',
            trigger: 'blur'
          }]
        } ];
      }
    },
    data() {
        return {
          form: {
            p1: null,
            p2: null,
            p3: null,
            p4: null,
            p5: null,
            p6: null,
          },
        };
    },
    methods: {
      getData() {
          return this.form;
      },
      validateData() {
          return new Promise((resolve) => {
            this.$refs["form"].validate((valid) => {
                resolve(valid);
            });
          });
      },
    },
};

export const formConfig = {
  fieldList: [{
    label: '1、在过去的任何时间里，您是否出现过喘息（呼吸过程中气道发出的持续、粗糙的声音）或哮鸣音（呼吸时伴随“咻咻”哨音）?',
    key: 'p1',
  }, {
    label: '1.1、在过去的12个月里，您是否出现过喘息或哮鸣音？',
    key: 'p2',
  }, {
    label: '1.2、在过去的12个月里，您发生过多少次喘息发作？（呼吸症状突然明显变差，喘息、胸闷、咳嗽明显加重，呼吸困难，以至于需要比平时用更多药物或者去看医生才能控制住即为一次喘息发作）',
    key: 'p3',
  }, {
    label: '1.3、在过去的12个月里，您平均多久出现因喘息而夜间憋醒?',
    key: 'p4',
  }, {
    label: '2、在过去的12个月里，您是否在运动中或运动后出现咳嗽、喘息或者哮鸣音?',
    key: 'p5',
  }, {
    label: '3、在过去的12个月里，除了因感冒或胸部感染引起的咳嗽外，您是否在夜间出现过干咳？',
    key: 'p6',
  } ]
}
</script>
<style scoped>
</style>
