<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            size="mini"
        >
            <el-form-item label="检查部位" prop="p1" style="width: 100%">
                <el-cascader v-model="form.p1" :show-all-levels="false" :options="options" :props="optionProps" style="width: 100%" @change="handleChange"></el-cascader>
            </el-form-item>
            <el-form-item prop="p2" label="超声所见" :required="true">
                <el-tabs v-model="tabIndex" v-loading="loading" type="border-card">
                    <el-tab-pane v-for="(tab, index) in tabList" :name="`${index}`" :key="tab.name" :label="tab.name">
                        <el-form size="mini" class="tag-form">
                            <el-row>
                                <el-col :span="24"> 
                                    <el-form-item label="指标" :required="true" label-width="60px" class="left-form-item">
                                        <el-checkbox-group v-model="targets[tab.number]"  @change="handleTagChange($event, tab)">
                                            <el-checkbox label="无" value="无" border>无</el-checkbox>
                                            <el-checkbox v-for="(item, index2) in tab.children" :key="`${index}-${index2}`" :label="item.labelStr" border>{{ item.name }}</el-checkbox>
                                        </el-checkbox-group>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row v-if="filterForm.p2[tab.number]">
                                <el-col :span="24">
                                    <div v-for="(item, index3) in filterForm.p2[tab.number]" :key="index3" style="display: flex; padding-left: 40px;margin-bottom: 10px;">
                                        <div style="margin-right: 10px;" :title="item.description">{{ item.name }}</div>
                                        <div style="flex: 1;">
                                            <template v-if="item.attribute == 1">
                                                <el-input v-model="item.value" @input="handleNumberInput($event, item, tab)" style="width: 200px" :title="item.description">
                                                    <template #append>{{ item.unit }}</template>
                                                </el-input>
                                            </template>
                                            <template v-else-if="item.attribute == 2 && item.tags">
                                                <template v-if="item.unitType == 1">
                                                    <el-input v-model="item.value" maxlength="50" @input="handleTextInput($event, item, tab)"></el-input>
                                                </template>
                                                <template v-else>
                                                    <el-checkbox-group v-model="item.value" style="margin-top: 0px;" @change="handleChildChange($event, item, tab)"> 
                                                        <el-checkbox v-for="(t, index3) in item.tags" :key="index3" :label="t" border>{{t}}</el-checkbox>
                                                    </el-checkbox-group>
                                                </template>
                                            </template>
                                        </div>
                                    </div>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="描述" label-width="60px" class="left-form-item"> 
                                        <el-input type="textarea" v-model="form.p2[tab.number].description" :rows="2" maxlength="1000" show-word-limit></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </el-tab-pane>
                </el-tabs>
            </el-form-item>
            <el-form-item prop="p3" label="超声提示" :required="true">
                <el-input type="textarea" v-model="form.p3" :rows="5" maxlength="1000" show-word-limit></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
// import trees from '@/utils/indictor-tree'
// import details from '@/utils/indictor-detail'
// import { getIndicator }  from '@/api/system'
export default {
    name: "AN0103",
    props: {
        formData: String,
        context: Object,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);   
        }
        let indicatorOptions = []
        if (this.context.indicatorTreeOptions && this.context.indicatorTreeOptions.length) {
            indicatorOptions = this.context.indicatorTreeOptions[0].children
        }
        // if (trees) {
        //     indicatorOptions = trees[0].children
        // }
        if (indicatorOptions && indicatorOptions.length > 0) {
            this.source = JSON.parse(JSON.stringify(indicatorOptions))
            this.options = JSON.parse(JSON.stringify(indicatorOptions))
            this.options = this.options.map((item) => {
                item.children = item.children.map((item) => {
                    return {...item, children: null}
                })
                return item
            })
        }
        this.init()
    },
    data() {
        return {
            form: {
                p1: [],
                p2: {},
                p3: '',
            },
            filterForm: {
                p1: null,
                p2: {},
                p3: '',
            },
            tabIndex: 0,
            loading: false,
            targets: {},
            source: [],
            tabList: [],
            options: [],
            optionProps: {
                multiple: true,
                label: 'name',
                value: 'number'
            },
            // 表单校验
            rules: {
                // p1:绑定的名字
                p1: [
                    {
                        required: true,
                        message: "请选择检查部位",
                        trigger: "blur",
                    },
                ],
                p2: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: (rule, value, callback) => {
                            const keys = Object.keys(this.form.p2)
                            if (keys.length == 0) {
                                return callback(new Error("填选择指标项"));
                            }
                            for (var i=0;i<keys.length;i++) {
                                const keys2 = Object.keys(this.form.p2[keys[i]]).filter(key => key != 'description')
                                if (keys2.length == 0) {
                                    return callback(new Error("填选择指标项"));
                                }
                                for (var j=0;j<keys2.length;j++) {
                                    if (!this.form.p2[keys[i]][keys2[j]] || this.form.p2[keys[i]][keys2[j]].length == 0) {
                                        return callback(new Error("填完善指标项内容"));
                                    }
                                }
                            }
                            if (keys.length != this.tabList.length) {
                                return callback(new Error("填选择指标项"));
                            }
                            callback();
                        },
                    },
                ],
                p3: [
                    {
                        required: true,
                        message: "请输入超声提示",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        getIndicatorDetailById(id) {
            return this.context.getIndicatorDetailById(id)
            // return getIndicator(id)
        },
        async init() {
            if (this.form && this.form.p1 && this.form.p1.length) {
                for (var n=0;n<this.form.p1.length;n++) {
                    const p = this.form.p1[n]
                    if (p.length == 2) {
                        for (var k=0;k<this.source.length;k++) {
                            const item = this.source[k]
                            if (item.number == p[0] && item.children) {
                                for (var j=0;j<item.children.length;j++) {
                                    const item2 = item.children[j]
                                    if (item2.number == p[1]) {
                                        if (item2.children) {
                                            for (var i=0;i<item2.children.length;i++) {
                                                if (this.form.p2[item2.number][item2.children[i].number]) {
                                                    if (item2.children[i].attribute == 2 && !item2.children[i].tags) {
                                                        const res = await this.getIndicatorDetailById(item2.children[i].id)
                                                        item2.children[i].tags = res.indicatorDescs.map(item3 => item3.value)
                                                    }
                                                    item2.children[i].value = this.form.p2[item2.number][item2.children[i].number]
                                                    item2.children[i].labelStr = JSON.stringify(item2.children[i])
                                                    if (!this.targets[item2.number]) {
                                                        this.targets[item2.number] = []
                                                    }
                                                    this.targets[item2.number].push(item2.children[i].labelStr)
                                                    if (!this.filterForm.p2[item2.number]) {
                                                        this.filterForm.p2[item2.number] = []
                                                    }
                                                    this.filterForm.p2[item2.number].push(item2.children[i])
                                                }
                                                
                                            }
                                        }
                                        this.tabList.push(item2)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        handleTagChange(e, tab) {
            if(this.form.p2[tab.number] == undefined) {
                this.$set(this.form.p2, tab.number, {})
            }
            let tags = e
            if (tags.length > 1) {
                if (tags.some((item, index) => {
                    if (item == '无' && index != 0) {
                        return true
                    }
                })) {
                    this.targets[tab.number] = ['无']
                    this.$set(this.filterForm.p2, tab.number, [])
                    this.form.p2[tab.number] = {'0': '无'}
                    return
                } else {
                    tags = tags.filter(item => item != '无')
                    this.targets[tab.number] = tags
                    delete this.form.p2[tab.number]['0']
                }
            } else if (tags[0] == '无') {
                this.form.p2[tab.number] = {'0': '无'}
                return
            } else {
                Object.keys(this.form.p2[tab.number]).forEach(key => {
                    tags.forEach(t => {
                        if (t.indexOf(key) == -1) {
                            delete this.form.p2[tab.number][key]
                        }
                    })
                })
            }

            const list = []
            tags.forEach(s => {
                var item = JSON.parse(s)
                if (item.attribute == 1 || item.unitType == 1) {
                    item.value = this.form.p2[tab.number][item.number]
                } else {
                    item.value = this.form.p2[tab.number][item.number] || []
                }
                this.form.p2[tab.number][item.number] = item.value
                list.push(item)
            })
            this.$set(this.filterForm.p2, tab.number, list)
        },
        handleChildChange(e, item, tab) {
            if(this.form.p2[tab.number] == undefined) {
                this.$set(this.form.p2, tab.number, {})
            }
            this.form.p2[tab.number][item.number] = e;
        },
        async handleChange(e) {
            this.loading = true
            let tabs = []
            this.form.p1 = e
            if (this.tabIndex > e.length - 1) {
                this.tabIndex = `${e.length - 1}`
            }
            for (var i=0;i<e.length;i++) {
                for (var j=0;j<this.source.length;j++) {
                    if (this.source[j].number == e[i][0]) {
                        for (var k=0;k<this.source[j].children.length;k++) {
                            if (this.source[j].children[k].number == e[i][1]) {
                                if (this.source[j].children[k].children) {
                                    for (var n=0;n<this.source[j].children[k].children.length;n++) {
                                        if (this.source[j].children[k].children[n].attribute == 2 && !this.source[j].children[k].children[n].tags) {
                                            const res = await this.getIndicatorDetailById(this.source[j].children[k].children[n].id)
                                            this.source[j].children[k].children[n].tags = res.indicatorDescs.map(item => item.value)
                                        }
                                    }
                                }
                                tabs.push(this.source[j].children[k])
                            }
                        }
                    }
                }
            }
            for(var i=0;i<tabs.length;i++) {
                if (this.targets[tabs[i].number] == undefined) {
                    this.$set(this.targets, tabs[i].number, [])
                    this.$set(this.form.p2, tabs[i].number, {})
                }
                if (tabs[i].children) {
                    for(var j=0;j<tabs[i].children.length;j++) {
                        tabs[i].children[j].labelStr = JSON.stringify(tabs[i].children[j])
                    }
                }
            }
            this.tabList = tabs
            this.loading = false
        },
        handleNumberInput(event, item, tab) {
            let value = event.replace(/[^0-9.]/g, '');
            // 处理多个小数点的情况
            const decimalParts = value.split('.');
            if (decimalParts.length > 2) {
            value = decimalParts[0] + '.' + decimalParts.slice(1).join('');
            }

            // 限制整数部分为3位
            if (decimalParts[0].length > 3) {
            value = decimalParts[0].substring(0, 3) + (decimalParts[1] ? '.' + decimalParts[1] : '');
            }

            // 限制小数部分为2位
            if (decimalParts.length > 1 && decimalParts[1].length > 2) {
            value = decimalParts[0] + '.' + decimalParts[1].substring(0, 2);
            }
            item.value = value;
            if(this.form.p2[tab.number] == undefined) {
                this.$set(this.form.p2, tab.number, {})
            }
            this.form.p2[tab.number][item.number] = value;
        },
        handleTextInput(e, item, tab) {
            if(this.form.p2[tab.number] == undefined) {
                this.$set(this.form.p2, tab.number, {})
            }
            this.form.p2[tab.number][item.number] = e;
        },
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            console.log('ooo', this.targets)
            console.log('aaa', this.filterForm)
            console.log('提交：', JSON.stringify(this.form))
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
    },
};
</script>
<style scoped>
/deep/.el-checkbox--mini.is-bordered {
    padding: 6px 15px 0 15px !important;
    border-radius: 3px;
    height: 28px;
  }
  
  /deep/.el-checkbox__input {
    display: none !important;
    white-space: nowrap;
    cursor: pointer;
    outline: 0;
    line-height: 1;
    vertical-align: middle;
  }
  
  /deep/.el-checkbox__label {
    padding-left: 0px;
  }
  
  /deep/.el-form-item__label {
    text-align: right;
    vertical-align: middle;
    /* float: left; */
    color: #606266;
    line-height: 40px;
    padding: 0 12px 0 0;
    box-sizing: border-box;
    font-weight: 450;
    font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
    color: #000000;
  }
  
  /deep/.el-checkbox-group {
    font-size: 0;
    margin-top: 7px;
  }
  
  /deep/.el-checkbox {
    color: #000000;
    font-weight: 400;
    line-height: 1;
    cursor: pointer;
    white-space: nowrap;
    outline: 0;
    margin-right: 10px;
    margin-bottom: 10px;
    margin-left: 0px !important;
  }

  /deep/.tag-form .left-form-item{
    display: flex;
  }

  /deep/.tag-form .left-form-item .el-form-item__content {
    margin-left: 0px !important;
    width: calc(100% - 60px);
  }
</style>
