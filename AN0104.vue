<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini">
        <el-form-item :key="index" :prop="`p${index + 1}`" :label="`${index + 1}、${op}`" v-for="(op ,index) in options">
          <template v-slot:label>
            <span style="font-weight: bold;">{{ index + 1 }}、{{ op }}</span>
            <el-form-item v-if="op == '其他系统'" :prop="`p${index + 1}_name`" style="display: inline-block;">
              <el-input v-model="form[`p${index + 1}_name`]" maxLength="100" style="width: 120px;margin-left: 10px;z-index: 1;" size="mini" clearable placeholder="其他系统描述"/>
            </el-form-item>
          </template>
          <el-radio-group v-model="form[`p${index + 1}`]">
            <el-radio label="正常" border/>
            <el-radio label="异常" border/>
            <el-radio label="未查" border/>
          </el-radio-group>
          <el-input v-show="form[`p${index + 1}`] == '异常'" v-model="form[`p${index + 1}1`]" maxLength="100" placeholder="异常情况描述" style="width: 100%;margin-top: 10px;" size="mini" clearable/>
        </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0104',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      form: {
        p1: undefined,
      },
      options: ['皮肤、黏膜', '淋巴结', '头颈部', '胸部', '腹部', '脊柱', '肌肉骨骼', '神经系统', '其他系统'],
      // 表单校验
      rules: {
        p1: [{ required: true, message: '皮肤、黏膜必须选择', trigger: 'blur' }],
        p2: [{ required: true, message: '淋巴结必须选择', trigger: 'blur' }],
        p3: [{ required: true, message: '头颈部必须选择', trigger: 'blur' }],
        p4: [{ required: true, message: '胸部必须选择', trigger: 'blur' }],
        p5: [{ required: true, message: '腹部必须选择', trigger: 'blur' }],
        p6: [{ required: true, message: '脊柱必须选择', trigger: 'blur' }],
        p7: [{ required: true, message: '肌肉骨骼必须选择', trigger: 'blur' }],
        p8: [{ required: true, message: '神经系统必须选择', trigger: 'blur' }],
        p9: [{ required: true, message: '其他系统必须选择', trigger: 'blur' }],
        p9_name: [{ required: true, message: '其他系统描述必须填写', trigger: 'blur' }],
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>
<style scoped>
/deep/.el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

/deep/.el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

/deep/.el-radio__label {
  padding-left: 0px;
}

/deep/.el-form-item__label {
  width: 100%;
  text-align: left;
  vertical-align: middle;
  float: left;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

/deep/.el-radio-group {
  font-size: 0;
  margin-top: 7px;
}

/deep/.el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}
</style>
