{
  "compilerOptions": {
    "target": "es2015",
    "module": "esnext",
    "strict": false,
    "jsx": "preserve",
    "moduleResolution": "node",
    // "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "baseUrl": ".",
    // "allowJs": true, // 允许 JavaScript 文件
    // "checkJs": false, // 不检查 JavaScript 文件
    "paths": {
      "@/*": ["./src/*"],
      "@action-form/*": ["./action-form/*"]
    }
  },
  "vueCompilerOptions": {
    "target": 2.7
  },
  "include": [
    "./src/**/*.ts",
    "./src/**/*.tsx",
    "./src/**/*.vue",
    "./src/**/*.js"
  ],
  "exclude": [
    "node_modules"
  ]
}
